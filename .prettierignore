# Dependencies
node_modules/

# Build outputs
.next/
out/
dist/
build/

# Cache directories
.cache/
.parcel-cache/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Generated files
*.d.ts
!src/**/*.d.ts

# Config files that shouldn't be formatted
*.config.js
*.config.ts
next.config.*
tailwind.config.*
postcss.config.*
