import { formatDate } from '@/utils/commonFunction';
import { SuperAdminType } from '@/utils/types';
import { FiTrash2 } from 'react-icons/fi';
import { LuPencil } from 'react-icons/lu';
import Pagination from './Pagination';

interface SuperAdminsTableProps {
  data: SuperAdminType[];
  onView: (user: SuperAdminType) => void;
  onEdit: (user: SuperAdminType) => void;
  onDelete: (id: string) => void;
}

export default function SuperAdminsTable({
  data,
  onView,
  onEdit,
  onDelete,
}: SuperAdminsTableProps) {
  return (
    <>
      <table className="min-w-full table-auto text-left text-[11px]">
        <thead className="text-[#76787A]">
          <tr>
            <th className="px-4 py-3 font-medium">Date</th>
            <th className="px-4 py-3 font-medium">User Name</th>
            <th className="px-4 py-3 font-medium">Role Name</th>
            <th className="px-4 py-3 font-medium">Status</th>
            <th className="px-4 py-3 text-center">Action</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-100">
          {data.map(row => (
            <tr key={row.id} className="hover:bg-[#E4FFF4]">
              <td className="px-4 py-3 text-[#050013]">
                {formatDate(row.createdAt)}
              </td>
              <td
                className="cursor-pointer px-4 py-3 font-medium text-[#050013]"
                onClick={() => onView(row)}
              >
                {row.firstName} {row.lastName}
              </td>
              <td className="px-4 py-3 text-[#050013]">{row.role}</td>
              <td className="px-4 py-3">
                <span
                  className={`flex items-center gap-1 ${
                    row.isActive ? 'text-[#13BB76]' : 'text-[#8C8B9F]'
                  }`}
                >
                  <span className="h-[6px] w-[6px] rounded-full bg-current" />
                  {row.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td className="px-4 py-3">
                <div className="flex justify-center gap-3">
                  <LuPencil
                    className="h-4 w-4 cursor-pointer text-[#050013]"
                    onClick={() => onEdit(row)}
                  />
                  <FiTrash2
                    onClick={() => onDelete(row.id)}
                    className="h-4 w-4 cursor-pointer text-[#050013]"
                  />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <Pagination currentPage={1} totalPages={2} onPageChange={() => {}} />
    </>
  );
}
