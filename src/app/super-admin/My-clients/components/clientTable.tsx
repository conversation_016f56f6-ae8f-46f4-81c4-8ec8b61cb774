import { formatDate } from '@/utils/commonFunction';
import { ClientType } from '@/utils/types';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FiTrash2 } from 'react-icons/fi';
import { LuPencil } from 'react-icons/lu';

interface ClientsTableProps {
  data: ClientType[];
  onDelete: (id: string) => void;
  onStatusChange: (id: string, status: string) => void;
}

export default function ClientsTable({
  data,
  onDelete,
  onStatusChange,
}: ClientsTableProps) {
  const { push } = useRouter();

  return (
    <table className="min-w-full table-auto text-left text-[11px]">
      <thead className="text-[#76787A]">
        <tr>
          <th className="px-4 py-3 font-medium text-nowrap">Client Name</th>
          <th className="px-4 py-3 font-medium text-nowrap">Date Added</th>
          <th className="px-4 py-3 font-medium text-nowrap">Primary Contact</th>
          <th className="px-4 py-3 font-medium text-nowrap">Status</th>
          <th className="px-4 py-3 text-center">Action</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-100">
        {data.map(row => (
          <tr key={row.id} className="hover:bg-[#E4FFF4]">
            <td className="px-4 py-3 text-nowrap text-[#050013]">
              <Link href={`My-clients/${row.id}`} className="text-[#050013]">
                {row.companyName}
              </Link>
            </td>
            <td className="px-4 py-3 text-nowrap text-[#050013]">
              {formatDate(row.createdAt)}
            </td>
            <td className="px-4 py-3 text-nowrap text-[#050013]">
              {row.primaryContactName}
            </td>
            <td
              className="cursor-pointer px-4 py-3"
              onClick={() =>
                onStatusChange(
                  row.id,
                  row.status === 'active' ? 'inactive' : 'active'
                )
              }
            >
              <span
                className={`flex items-center gap-1 ${
                  row.status === 'active' ? 'text-[#13BB76]' : 'text-[#8C8B9F]'
                }`}
              >
                <span className="h-[6px] w-[6px] rounded-full bg-current" />
                <span className="capitalize">{row.status}</span>
              </span>
            </td>
            <td className="px-4 py-3">
              <div className="flex justify-center gap-3">
                <LuPencil
                  className="h-4 w-4 cursor-pointer text-[#050013]"
                  onClick={() => push(`My-clients/edit/${row.id}`)}
                />
                <FiTrash2
                  className="h-4 w-4 cursor-pointer text-[#050013]"
                  onClick={() => onDelete(row.id)}
                />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
