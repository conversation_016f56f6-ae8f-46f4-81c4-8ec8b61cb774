'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { FiEdit } from 'react-icons/fi';
import { GoDownload } from 'react-icons/go';
import { FiFilter, FiDownload, FiMoreVertical } from 'react-icons/fi';
import { IoIosSearch } from 'react-icons/io';
import { RxCounterClockwiseClock } from 'react-icons/rx';
import { TbUsers } from 'react-icons/tb';
import { BsFilter } from 'react-icons/bs';

export default function Setting() {
  const [selectedUser, setSelectedUser] = useState(null);
  const [activeTab, setActiveTab] = useState('general');

  const members = [
    {
      id: 'TechNova',
      name: '11-01-2025',
      contact: '10',
      status: 'Active',
      city: 'Basic',
      rides: 'abc',
    },
    {
      id: 'TechNova',
      name: '11-01-2025',
      contact: '10',
      status: 'Suspended',
      city: 'Basic',
      rides: 'abc',
    },
  ];

  const accessSections = [
    {
      title: 'User & Role Management',
      items: [
        'Create, edit, delete Admins, Dispatchers, Drivers, Users',
        'Assign Admins to taxi companies',
        'Manage permissions for all roles',
      ],
    },
    {
      title: 'Booking & Dispatch Control',
      items: [
        'View, edit, and manage all bookings',
        'Assign/reassign drivers to rides',
        'View driver live locations',
      ],
    },
    {
      title: 'Company & Financial Management',
      items: [
        'Add, edit, or remove taxi companies',
        'Manage company subscription plans',
        'View & modify company-specific pricing and fare structures',
      ],
    },
    {
      title: 'System & Policy Settings',
      items: [
        'Define platform-wide fare policies',
        'Set geofencing rules & restrictions',
        'Control global discount and promo policies',
      ],
    },
    {
      title: 'Reporting & Analytics',
      items: [
        'View and export reports on revenue, ride activity, and system performance',
        'Monitor driver performance & customer ratings',
        'Analyze dispatcher efficiency',
      ],
    },
  ];

  const userDetails = {
    name: 'Silverline Solutions',
    regestration: '*********',
    tax: '*********',
    businesstype: 'LLC',
    url: 'www.silverline.com',
  };

  return (
    <div className="rounded-lg border border-gray-200">
      {/* Top Row */}
      <div className="flex flex-col justify-between gap-6 rounded-t-lg border-b bg-white p-6 lg:flex-row lg:items-start">
        {/* Left: Logo + Company Info */}
        <div className="flex min-w-[250px] items-center gap-4">
          <div className="flex h-18 w-18 items-center justify-center rounded-full bg-[#E4FFF4] text-[24px] font-normal text-[#4D695D]">
            TS
          </div>
          <div>
            <h2 className="text-[24px] font-normal text-[#050013]">
              TechNova Solutions
            </h2>
            <div className="flex items-center gap-2 text-[11px] font-medium text-[#13BB76]">
              <span className="h-2 w-2 rounded-full bg-[#13BB76]"></span>
              Active
            </div>
          </div>
        </div>

        {/* Middle: Contact Details */}
        <div className="flex flex-1 flex-col border-l text-sm sm:flex-row sm:items-start sm:justify-center sm:gap-12">
          <div className="space-y-3">
            <p className="text-[14px] font-medium text-[#050013]">
              <span className="text-[13px] font-normal text-[#76787A]">
                Email Id :{' '}
              </span>{' '}
              <EMAIL>
            </p>
            <p className="text-[14px] font-medium text-[#050013]">
              <span className="text-[13px] font-normal text-[#76787A]">
                Phone no :{' '}
              </span>{' '}
              *************
            </p>
            <p className="text-[14px] font-medium text-[#050013]">
              <span className="text-[13px] font-normal text-[#76787A]">
                Subscription Plan :{' '}
              </span>{' '}
              Basic
            </p>
          </div>
          <div className="space-y-3">
            <p className="text-[14px] font-medium text-[#050013]">
              <span className="text-[13px] font-normal text-[#76787A]">
                Primary Contact Name :{' '}
              </span>{' '}
              Oliver Thompson
            </p>
            <p className="text-[14px] font-medium text-[#050013]">
              <span className="text-[13px] font-normal text-[#76787A]">
                Tax Identification Number (TIN/VAT):{' '}
              </span>{' '}
              45485
            </p>
            <p className="text-[14px] font-medium text-[#050013]">
              <span className="text-[13px] font-normal text-[#76787A]">
                Address :{' '}
              </span>{' '}
              12 Street, The Hague, Netherlands
            </p>
            <p className="text-right">
              <button
                onClick={() => setSelectedUser(userDetails)}
                className="text-[13px] text-[#3324E3] underline"
              >
                View More
              </button>
            </p>
          </div>
        </div>

        {/* Right: Action Buttons */}
        <div className="flex items-center justify-end gap-1">
          <button
            className="rounded-md p-2 text-[#76787A] hover:bg-gray-100"
            title="Edit"
          >
            <FiEdit size={18} />
          </button>
          <button
            className="rounded-md p-2 text-[#76787A] hover:bg-gray-100"
            title="Download"
          >
            <GoDownload size={20} />
          </button>
        </div>
      </div>

      {/* Overview Section */}
      <div className="rounded-b-lg bg-[#F6F8FB] pt-6">
        <h3 className="ml-[20px] flex w-fit items-center gap-2 border-b border-b-[3px] border-[#3324E3] pb-1 text-[14px] font-normal text-[#3324E3]">
          <TbUsers size={20} />
          Passengers
        </h3>
        <div className="divide-y overflow-x-auto border-t">
          <div className="header-bar bg-table-head flex items-center justify-between rounded-t-lg px-3 py-2">
            <form className="max-w-md flex-1">
              <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-[#76787A]">
                  <IoIosSearch size={20} />
                </div>
                <input
                  id="default-search"
                  type="search"
                  placeholder="Search here"
                  className="block w-3/4 rounded-full border border-0 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                />
              </div>
            </form>

            <div className="flex items-center gap-2">
              <div
                className="relative inline-block text-left"
                data-headlessui-state=""
              >
                <button
                  id="headlessui-menu-button-r2"
                  type="button"
                  aria-haspopup="menu"
                  aria-expanded="false"
                  data-headlessui-state=""
                  className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <BsFilter size={18} />
                  Status
                </button>
              </div>

              <div
                className="relative inline-block text-left"
                data-headlessui-state=""
              >
                <button
                  id="headlessui-menu-button-r2"
                  type="button"
                  aria-haspopup="menu"
                  aria-expanded="false"
                  data-headlessui-state=""
                  className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <BsFilter size={18} />
                  Company
                </button>
              </div>

              <div
                className="relative inline-block text-left"
                data-headlessui-state=""
              >
                <button
                  id="headlessui-menu-button-r2"
                  type="button"
                  aria-haspopup="menu"
                  aria-expanded="false"
                  data-headlessui-state=""
                  className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <BsFilter size={18} />
                  City
                </button>
              </div>

              <div
                className="relative inline-block text-left"
                data-headlessui-state=""
              >
                <button
                  id="headlessui-menu-button-r2"
                  type="button"
                  aria-haspopup="menu"
                  aria-expanded="false"
                  data-headlessui-state=""
                  className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <FiFilter size={18} />
                  Filters
                </button>
              </div>

              <button
                type="button"
                aria-label="clock"
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <RxCounterClockwiseClock size={22} />
              </button>

              <button
                aria-label="download"
                type="button"
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <FiDownload size={18} />
              </button>
            </div>
          </div>
          <table className="min-w-full divide-y divide-gray-200 text-[12px]">
            <thead>
              <tr>
                <th className="px-4 py-3">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </th>
                {[
                  'ID',
                  'Name',
                  'Contact No',
                  'Status',
                  'City',
                  'Rides',
                  '',
                ].map(header => (
                  <th
                    key={header}
                    className="px-4 py-3 text-left font-medium whitespace-nowrap text-[#76787A]"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {members.map((member, idx) => (
                <tr
                  key={idx}
                  className="cursor-pointer transition hover:bg-[#E4FFF4]"
                >
                  <td className="px-4 py-3 text-center">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                  </td>
                  <td className="flex items-center gap-2 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                    {/* Client Name */}
                    {member.id}
                  </td>

                  <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                    {member.name}
                  </td>
                  <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                    {member.contact}
                  </td>
                  <td
                    className={`flex items-center gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap ${
                      member.status === 'Active'
                        ? 'text-[#13BB76]' // green
                        : member.status === 'Suspended'
                          ? 'text-[#DC3545]' // red (you can change to any color you want)
                          : ''
                    }`}
                  >
                    <span
                      className={`inline-block h-2 w-2 rounded-full ${
                        member.status === 'Active'
                          ? 'bg-[#13BB76]'
                          : member.status === 'Suspended'
                            ? 'bg-[#13BB76]'
                            : ''
                      }`}
                    ></span>
                    {member.status}
                  </td>
                  <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                    {member.city}
                  </td>
                  <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                    {member.rides}
                  </td>
                  <td className="cursor-pointer px-4 py-3 text-[16px] text-[#76787A] select-none">
                    <FiMoreVertical />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* View detail modal */}
      {selectedUser && (
        <div className="custom-scrollbar fixed inset-0 z-[999] flex">
          <div
            className="absolute inset-0 bg-black/30"
            onClick={() => setSelectedUser(null)}
          ></div>

          <div className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out">
            {/* Header */}
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                View Details
              </h2>
              <button
                onClick={() => setSelectedUser(null)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            {/* Tabs */}
            <div className="flex justify-start gap-6 border-b border-gray-200 px-6">
              {['general', 'permissions'].map(tab => (
                <button
                  key={tab}
                  className={`relative px-2 py-2 text-center text-[13px] font-normal ${
                    activeTab === tab
                      ? 'font-semibold text-[#3324E3]'
                      : 'text-gray-500'
                  }`}
                  onClick={() => setActiveTab(tab)}
                >
                  <span className="relative inline-block">
                    {tab === 'general' ? 'General Info' : 'Permissions'}
                    {activeTab === tab && (
                      <span className="absolute right-0 -bottom-[8px] left-0 mx-auto w-full border-b-2 border-[#3324E3]"></span>
                    )}
                  </span>
                </button>
              ))}
            </div>

            <div className="bg-tables space-y-4 px-6 py-6 text-[13px]">
              {/* General Info Tab */}
              {activeTab === 'general' && (
                <>
                  <div>
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Company Information
                    </h3>
                    <div className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Company Name
                        </span>{' '}
                        {selectedUser.name}
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Legal Name
                        </span>{' '}
                        ABC Transport Solutions Pvt. Ltd.
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Company Registration Number
                        </span>{' '}
                        {selectedUser.regestration}
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Tax Identification Number (TIN/VAT)
                        </span>{' '}
                        {selectedUser.tax}
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Business Type:
                        </span>{' '}
                        {selectedUser.businesstype}
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Website URL
                        </span>{' '}
                        {selectedUser.url}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Contact Information
                    </h3>
                    <p className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Primary Contact Name
                        </span>{' '}
                        Toyota Prius
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Designation
                        </span>{' '}
                        Owner
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Email Address
                        </span>{' '}
                        <EMAIL>
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Phone Number
                        </span>{' '}
                        ****** 567 890
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Alternative Contact
                        </span>{' '}
                        ******* 567 890
                      </div>
                    </p>
                  </div>
                  <div>
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Business Address
                    </h3>
                    <p className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Head Office
                        </span>{' '}
                        123 Main Street, New York, NY
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          City
                        </span>{' '}
                        New York
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          State/Province:
                        </span>{' '}
                        New York
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Country
                        </span>{' '}
                        USA
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Postal Code
                        </span>{' '}
                        100001
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Branch Locations
                        </span>{' '}
                        Los Angeles, CA Chicago,IL
                      </div>
                    </p>
                  </div>

                  <div>
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Payment & Billing
                    </h3>
                    <p className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Billing Cycle
                        </span>{' '}
                        Monthly
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Invoice Email
                        </span>{' '}
                        <EMAIL>
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Contract
                        </span>
                        <div className="flex items-center gap-2">
                          <img
                            src="/images/contract.png"
                            alt="Contract Icon"
                            className="h-4 w-4"
                          />
                          <span className="underline">View Contract</span>
                        </div>
                      </div>

                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Legal Representative:
                        </span>{' '}
                        John Doe
                      </div>
                    </p>
                  </div>

                  <div>
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Service Details
                    </h3>
                    <p className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Type of Service
                        </span>{' '}
                        Standard Taxi
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Operating Regions
                        </span>{' '}
                        Newyork, Los Angeles, Chicago
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Fleet Size
                        </span>{' '}
                        150 Vehicles
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Available Vehicle Types
                        </span>{' '}
                        Sedan, SUV
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Special Services
                        </span>
                        <div className="flex items-center gap-2">
                          <img
                            src="/images/chair.svg"
                            alt="Contract Icon"
                            className="h-6 w-6"
                          />
                          <img
                            src="/images/seat-belt.svg"
                            alt="Contract Icon"
                            className="h-6 w-6"
                          />
                        </div>
                      </div>
                    </p>
                  </div>

                  <div>
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Licensing & Compliance
                    </h3>
                    <p className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Business License
                        </span>
                        <div className="flex items-center gap-2">
                          <img
                            src="/images/contract.png"
                            alt="Contract Icon"
                            className="h-4 w-4"
                          />
                          <span className="underline">View Contract</span>
                        </div>
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Insurance Company
                        </span>{' '}
                        HeapHealth
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Policy Number
                        </span>{' '}
                        78451221
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Expiry
                        </span>{' '}
                        12-12-2025
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Regulatory Certificated
                        </span>{' '}
                        <span className="underline">
                          Transport Authority Approval
                        </span>
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Permit Expiry
                        </span>{' '}
                        10-03-2026
                      </div>
                    </p>
                  </div>
                </>
              )}

              {/* Permissions Tab */}
              {activeTab === 'permissions' && (
                <div className="mb-12">
                  <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                    Access Control and permissions
                  </h3>
                  {accessSections.map((section, index) => (
                    <div key={index}>
                      <h4 className="mt-6 px-4 py-2 text-[13px] font-semibold text-[#76787A]">
                        {section.title}
                      </h4>
                      <ul className="ml-5 list-disc px-6 text-[14px] text-[#050013]">
                        {section.items.map((item, idx) => (
                          <li className="my-2" key={idx}>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}

                  <div className="mt-6">
                    <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                      Edit Logs
                    </h3>
                    <p className="grid grid-cols-2 gap-2 px-4 py-4">
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Last Modified
                        </span>{' '}
                        20-01-2025
                      </div>
                      <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                        <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                          Last Modified By
                        </span>{' '}
                        Jack Manosh
                      </div>
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
