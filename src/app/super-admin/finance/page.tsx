'use client';

import React, { useState } from 'react';
import { FiFilter, FiDownload, FiMoreVertical } from 'react-icons/fi';
import { IoIosSearch } from 'react-icons/io';
import { RxCounterClockwiseClock } from 'react-icons/rx';
import Link from 'next/link';

export default function Setting() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  const data = [
    {
      id: 1542,
      planName: 'Premium',
      planType: 'Monthly',
      startDate: '01-02-2024',
      endDate: '01-03-2024',
      status: { text: 'Active', color: 'text-green-600', dot: 'bg-green-500' },
      totalRides: 500,
      ridesUsed: 250,
      ridesLeft: 250,
      lastBillingDate: '01-04-2024',
      paymentStatus: { text: 'Unpaid', color: 'text-orange-500' },
      method: '/paypal.png',
    },
    {
      id: 7854,
      planName: 'Premium',
      planType: 'Monthly',
      startDate: '01-12-2024',
      endDate: '01-01-2024',
      status: { text: 'Expired', color: 'text-gray-500', dot: 'bg-blue-500' },
      totalRides: 500,
      ridesUsed: 500,
      ridesLeft: 0,
      lastBillingDate: 'NA',
      paymentStatus: { text: 'Paid', color: 'text-green-500' },
      method: '/paypal.png',
    },
    {
      id: 1548,
      planName: 'Basic',
      planType: 'Monthly',
      startDate: '01-11-2023',
      endDate: '01-12-2024',
      status: { text: 'Inactive', color: 'text-gray-500', dot: 'bg-gray-400' },
      totalRides: 500,
      ridesUsed: 500,
      ridesLeft: 0,
      lastBillingDate: 'NA',
      paymentStatus: { text: 'Paid', color: 'text-green-500' },
      method: '/upi.png',
    },
    {
      id: 8956,
      planName: 'Premium',
      planType: 'Custom',
      startDate: '01-10-2023',
      endDate: '01-11-2023',
      status: { text: 'Inactive', color: 'text-gray-500', dot: 'bg-pink-500' },
      totalRides: 1,
      ridesUsed: 1,
      ridesLeft: 0,
      lastBillingDate: 'NA',
      paymentStatus: { text: 'Paid', color: 'text-green-500' },
      method: '/paypal.png',
    },
    {
      id: 4523,
      planName: 'Premium',
      planType: 'Monthly',
      startDate: '01-09-2023',
      endDate: '01-10-2023',
      status: { text: 'Inactive', color: 'text-gray-500', dot: 'bg-green-500' },
      totalRides: 500,
      ridesUsed: 500,
      ridesLeft: 0,
      lastBillingDate: 'NA',
      paymentStatus: { text: 'Paid', color: 'text-green-500' },
      method: '/paypal.png',
    },
  ];

  const TABLE_HEADERS = [
    'Subscription ID',
    'Plan Name',
    'Plan Type',
    'Start Date',
    'End Date',
    'Status',
    'Total Rides',
    'Rides Used',
    'Rides Left',
    'Last Billing Date',
    'Payment Status',
    'Method',
  ];

  return (
    <div className="rounded-lg bg-white shadow">
      {/* Top Bar */}
      <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
        <form className="max-w-md flex-1">
          <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
            Search
          </label>
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-[#76787A]">
              <IoIosSearch size={20} />
            </div>
            <input
              id="default-search"
              type="search"
              placeholder="Search here"
              className="block w-3/4 rounded-full border border-0 border-gray-300 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
            />
          </div>
        </form>

        <div className="flex items-center gap-2">
          <div
            className="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              id="headlessui-menu-button-r2"
              type="button"
              aria-haspopup="menu"
              aria-expanded="false"
              data-headlessui-state=""
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <FiFilter size={18} />
              Filters
            </button>
          </div>

          <button
            type="button"
            aria-label="clock"
            className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <RxCounterClockwiseClock size={22} />
          </button>

          <button
            aria-label="download"
            type="button"
            className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <FiDownload size={18} />
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="text-[11px] text-[#76787A]">
              {TABLE_HEADERS.map(header => (
                <th key={header} className="p-3 text-left font-medium">
                  {header}
                </th>
              ))}
              <th></th>
            </tr>
          </thead>
          <tbody>
            {data.map((row, idx) => (
              <tr key={idx} className="border-b text-sm hover:bg-[#E4FFF4]">
                <td
                  className="cursor-pointer p-3 text-[11px] font-normal text-[050013]"
                  onClick={() => setIsModalOpen(true)}
                >
                  {row.id}
                </td>
                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.planName}
                </td>
                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.planType}
                </td>
                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.startDate}
                </td>

                {/* End Date */}
                <td className="p-3 align-middle text-[11px] font-medium text-[050013]">
                  <div className="flex items-center gap-2">
                    <span
                      className={`h-2 w-2 rounded-full ${row.status.dot}`}
                    ></span>
                    {row.endDate}
                  </div>
                </td>

                {/* Status */}
                <td className="p-3 align-middle text-[11px] font-medium font-normal">
                  <div className="flex items-center gap-2">
                    <span
                      className={`h-2 w-2 rounded-full ${
                        row.status.text === 'Active'
                          ? 'bg-[#13BB76]'
                          : row.status.text === 'Inactive'
                            ? 'bg-[#76787A]'
                            : 'bg-orange-500'
                      }`}
                    ></span>
                    <span
                      className={
                        row.status.text === 'Active'
                          ? 'text-[#13BB76]'
                          : row.status.text === 'Inactive'
                            ? 'text-[#76787A]'
                            : 'text-orange-500'
                      }
                    >
                      {row.status.text}
                    </span>
                  </div>
                </td>

                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.totalRides}
                </td>
                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.ridesUsed}
                </td>
                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.ridesLeft}
                </td>
                <td className="p-3 text-[11px] font-normal text-[050013]">
                  {row.lastBillingDate}
                </td>

                {/* Payment Status with dot + text */}
                <td className="flex items-center gap-2 p-3 text-[11px] font-medium">
                  <span
                    className={`h-2 w-2 rounded-full ${
                      row.paymentStatus.text === 'Paid'
                        ? 'bg-green-500'
                        : 'bg-orange-500'
                    }`}
                  ></span>
                  <span
                    className={
                      row.paymentStatus.text === 'Paid'
                        ? 'text-green-500'
                        : 'text-orange-500'
                    }
                  >
                    {row.paymentStatus.text}
                  </span>
                </td>

                <td className="text-[11px] font-normal text-[050013]">
                  <img
                    src={'/images/paypal.svg'}
                    alt="Method"
                    className="h-10 w-10"
                  />
                </td>
                <td className="p-3 text-[18px] font-normal text-[050013]">
                  <FiMoreVertical className="cursor-pointer text-[#76787A]" />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {isModalOpen && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div
            className="absolute inset-0 bg-black/30"
            onClick={() => setSelectedUser(null)}
          ></div>
          <div className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out">
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                View User Details
              </h2>
              <button
                type="button"
                onClick={() => {
                  console.log('Close button clicked');
                  setSelectedUser(null);
                }}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <div className="bg-tables space-y-4 px-6 py-6 text-[13px]">
              <div>
                <h3 className="mb-1 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                  Subscription Details
                </h3>
                <p className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Subscription ID
                    </span>{' '}
                    1542
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Plan Name
                    </span>{' '}
                    Premium
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Plan Type
                    </span>{' '}
                    Monthly
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Start Date
                    </span>{' '}
                    01-02-2024
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      End Date
                    </span>{' '}
                    01-03-2024
                  </div>
                  <div className="mb-4 grid text-[12px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Status:
                    </span>
                    <span className="inline-flex items-center gap-1 text-[#13BB76]">
                      <span className="h-2 w-2 rounded-full bg-[#13BB76]"></span>
                      Active
                    </span>
                  </div>
                </p>
              </div>

              <div>
                <h3 className="mb-1 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                  Ride Details
                </h3>
                <p className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Total Rides
                    </span>{' '}
                    500
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Rides Used
                    </span>{' '}
                    250
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Rides Left
                    </span>{' '}
                    250
                  </div>
                </p>
              </div>

              <div>
                <h3 className="mb-1 rounded-full bg-white px-4 py-2 text-[13px] font-medium text-[#76787A]">
                  Payment Details
                </h3>
                <p className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Billing Date
                    </span>{' '}
                    01-04-2024
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Oustanding Balance
                    </span>
                    <span className="inline-flex items-center gap-1 text-[#050013]">
                      <span className="h-2 w-2 rounded-full bg-[#13BB76]"></span>
                      € 388.00
                    </span>
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Payment Status
                    </span>
                    <span className="inline-flex items-center gap-1 text-[12px] text-[#FFC107]">
                      <span className="h-2 w-2 rounded-full bg-[#13BB76]"></span>
                      Unpaid
                    </span>
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Payment Method
                    </span>
                    <img
                      src="/images/paypal.svg"
                      alt="Icon"
                      className="object-contain"
                    />
                  </div>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
