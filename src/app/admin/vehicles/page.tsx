import React from 'react';
import { FiFilter, FiDownload, FiMoreVertical } from 'react-icons/fi';
import { IoIosSearch } from 'react-icons/io';
import { MdOutlineStar } from 'react-icons/md';
import { RxCounterClockwiseClock } from 'react-icons/rx';

import Link from 'next/link';

import {
  FaUserTie,
  FaTruck,
  FaDollarSign,
  FaCheckCircle,
} from 'react-icons/fa';

// statusColors.ts
export const STATUS_COLORS: Record<string, { text: string; dot: string }> = {
  Active: { text: 'text-[#13BB76]', dot: 'bg-[#13BB76]' },
  Inactive: { text: 'text-[#76787A]', dot: 'bg-[#76787A]' },
  Suspended: { text: 'text-[#050013]', dot: 'bg-[#050013]' },
  Enroute: { text: 'text-[#1E90FF]', dot: 'bg-[#1E90FF]' },
  'On trip': { text: 'text-[#FF8C00]', dot: 'bg-[#FF8C00]' },
};

// verificationColors.ts
export const VERIFICATION_COLORS: Record<
  string,
  { text: string; dot: string }
> = {
  Verified: { text: 'text-[#13BB76]', dot: 'bg-[#13BB76]' },
  Rejected: { text: 'text-[#76787A]', dot: 'bg-[#76787A]' },
  Pending: { text: 'text-[#FF8C00]', dot: 'bg-[#FF8C00]' },
};

export default function vehicle() {
  const stats = [
    {
      title: 'Total Active Vehicles',
      value: '14',
      icon: <img src="/images/active-driver.svg" alt="Active Drivers" />,
      color: 'bg-green-100 text-green-500',
    },
    {
      title: 'Verified Vehicles',
      value: '11',
      icon: (
        <img src="/images/unassigned-driver.svg" alt="Unassigned Drivers" />
      ),
      color: 'bg-red-100 text-red-500',
    },
    {
      title: 'Pending Verification',
      value: '03',
      icon: <img src="/images/total-earning.svg" alt="Unassigned Drivers" />,
      color: 'bg-green-100 text-green-500',
    },
    {
      title: 'Expired Insurance',
      value: '03',
      icon: (
        <img src="/images/pending-verification.svg" alt="Unassigned Drivers" />
      ),
      color: 'bg-yellow-100 text-yellow-500',
    },
  ];
  // statusConfig.js
  const STATUS_CONFIG = {
    Active: { textClass: 'text-[#13BB76]', dotClass: 'bg-[#13BB76]' },
    Inactive: { textClass: 'text-[#76787A]', dotClass: 'bg-[#76787A]' },
    Suspended: { textClass: 'text-[#050013]', dotClass: 'bg-[#050013]' },
    Enroute: { textClass: 'text-[#1E90FF]', dotClass: 'bg-[#1E90FF]' },
    'On trip': { textClass: 'text-[#FF8C00]', dotClass: 'bg-[#FF8C00]' },
  };

  const members = [
    {
      id: '124432',
      name: 'Emily Davis',
      contact: '+1234567891',
      status: 'Active',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Verified',
    },
    {
      id: '124432',
      name: 'Emily Davis',
      contact: '+1234567891',
      status: 'Active',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Verified',
    },
    {
      id: '124432',
      name: 'Emily Davis',
      contact: '+1234567891',
      status: 'Enroute',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Pending',
    },
    {
      id: '124432',
      name: 'Emily Davis',
      contact: '+1234567891',
      status: 'Suspended',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Rejected',
    },
    {
      id: '124432',
      name: 'Emily Davis',
      contact: '+1234567891',
      status: 'Inactive',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Pending',
    },
    {
      id: '124432',
      name: 'Emily Davis',
      contact: '+1234567891',
      status: 'On trip',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Pending',
    },
    {
      id: '124433',
      name: 'Morris Seal',
      contact: '+1234567891',
      status: 'Inactive',
      shift: 'Day Shift',
      rating: '4.5',
      region: 'Eindhoven',
      vdetail: 'EV  MT-9826-SA',
      rides: '183',
      earning: '€16,000',
      verification: 'Rejected',
    },
  ];

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-end">
        <button className="rounded-full bg-[#3707EF] px-4 py-2 text-sm text-white transition hover:bg-[#4338ca]">
          + Add New Vehicle
        </button>
      </div>

      <div className="my-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item, index) => (
          <div
            key={index}
            className="flex items-center justify-between rounded-xl border bg-white p-4"
          >
            <div>
              <p className="text-[13px] font-normal text-[#76787A]">
                {item.title}
              </p>
              <h2 className="text-[26px] font-medium text-[#050013]">
                {item.value}
              </h2>
            </div>
            <div
              className={`flex h-10 w-10 items-center justify-center rounded-full ${item.color}`}
            >
              {item.icon}
            </div>
          </div>
        ))}
      </div>

      <div className="overflow-x-auto rounded-lg border bg-white">
        <div className="header-bar bg-table-head flex items-center justify-between rounded-t-lg px-3 py-1">
          <form className="max-w-md flex-1">
            <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Search
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-[#76787A]">
                <IoIosSearch size={20} />
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                className="block w-3/4 rounded-full border border-0 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              />
            </div>
          </form>

          <div className="flex items-center gap-2">
            <div
              className="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                id="headlessui-menu-button-r2"
                type="button"
                aria-haspopup="menu"
                aria-expanded="false"
                data-headlessui-state=""
                className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <FiFilter size={18} />
                Filters
              </button>
            </div>

            <button
              type="button"
              aria-label="clock"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <RxCounterClockwiseClock size={22} />
            </button>

            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <FiDownload size={16} />
            </button>
          </div>
        </div>
        <table className="min-w-full divide-y divide-gray-200 text-[12px]">
          <thead>
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              {[
                'ID',
                'Name',
                'Contact No',
                'Status',
                'Shift',
                'Rating',
                'Region',
                'Vehicle Details',
                'Rides',
                'Earnings',
                'Verification',
                '',
              ].map(header => (
                <th
                  key={header}
                  className="px-4 py-3 text-left font-medium whitespace-nowrap text-[#76787A]"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {members.map((member, idx) => (
              <tr
                key={idx}
                className="cursor-pointer transition hover:bg-[#E4FFF4]"
              >
                <td className="px-4 py-3 text-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </td>

                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.id}
                </td>

                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.name}
                </td>

                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.contact}
                </td>

                {/* STATUS column */}
                <td
                  className={`flex items-center gap-1 px-4 py-3 text-[11px] font-normal whitespace-nowrap ${
                    STATUS_COLORS[member.status]?.text || ''
                  }`}
                >
                  <span
                    className={`inline-block h-2 w-2 rounded-full ${
                      STATUS_COLORS[member.status]?.dot || ''
                    }`}
                  ></span>
                  {member.status}
                </td>

                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.shift}
                </td>

                <td className="flex items-center gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.rating}
                  <MdOutlineStar size={14} className="text-[#F79E18]" />
                </td>

                <td className="gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.region}
                </td>

                <td className="gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.vdetail}
                </td>

                <td className="gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.rides}
                </td>

                <td className="gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.earning}
                </td>

                {/* VERIFICATION column */}
                <td
                  className={`flex items-center gap-1 px-4 py-3 text-[11px] font-normal whitespace-nowrap ${
                    VERIFICATION_COLORS[member.verification]?.text || ''
                  }`}
                >
                  <span
                    className={`inline-block h-2 w-2 rounded-full ${
                      VERIFICATION_COLORS[member.verification]?.dot || ''
                    }`}
                  ></span>
                  {member.verification}
                </td>

                <td className="cursor-pointer px-4 py-3 text-[16px] text-[#76787A] select-none">
                  <FiMoreVertical />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
