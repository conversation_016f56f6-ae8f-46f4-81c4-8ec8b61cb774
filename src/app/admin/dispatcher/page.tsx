'use client';

import React, { useState, useRef, useEffect } from 'react';
import { LuPencil } from 'react-icons/lu';
import { FaChevronDown, FaCheck } from 'react-icons/fa6';
import { RxCounterClockwiseClock } from 'react-icons/rx';
import { FiFilter, FiDownload, FiMoreVertical, FiTrash2 } from 'react-icons/fi';
import { IoIosSearch } from 'react-icons/io';
import { HiPlus } from 'react-icons/hi';
import Link from 'next/link';

function AccordionSection({ title, items }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-4">
      <div
        className="flex cursor-pointer items-center justify-between pb-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <label className="flex cursor-pointer items-center gap-3">
          <div className="relative flex items-center">
            <input type="checkbox" className="peer hidden" />
            <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]">
              {' '}
            </span>
            <FaCheck
              size={12}
              className="absolute left-[2px] transition-opacity duration-200 peer-checked:text-[#18EC94] peer-checked:opacity-100 peer-[&:not(:checked)]:opacity-0"
            />
          </div>
          <span className="text-[14px] font-medium text-[#050013] peer-checked:text-[#050013]">
            {title}
          </span>
        </label>
        <span className="bg-tables rounded-full p-1">
          <FaChevronDown
            className={`h-3 w-3 text-gray-500 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : 'rotate-0'
            }`}
          />
        </span>
      </div>

      {isOpen && (
        <div className="mt-2 mb-6">
          {items.map((item, idx) => (
            <label
              key={idx}
              className="mb-2 flex cursor-pointer items-center space-x-2"
            >
              <div className="relative flex items-center">
                <input type="checkbox" className="peer hidden" />
                <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]">
                  {' '}
                </span>
                <FaCheck
                  size={12}
                  className="absolute left-[2px] transition-opacity duration-200 peer-checked:text-[#18EC94] peer-checked:opacity-100 peer-[&:not(:checked)]:opacity-0"
                />
              </div>
              <span className="text-sm text-[#76787A] peer-checked:text-[#050013]">
                {item}
              </span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}

export default function Dispatcher() {
  const [activeTab, setActiveTab] = useState('Super Admins');
  const [search, setSearch] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [editUser, setEditUser] = useState(null);
  const modalRef = useRef(null);

  const superAdmins = [
    { date: '11-01-2024', name: 'John', status: 'Active' },
    { date: '11-01-2024', name: 'Dave', status: 'Active' },
    { date: '11-01-2024', name: 'Monty', status: 'Active' },
    { date: '26-12-2024', name: 'Sam', status: 'Inactive' },
  ];

  const clients = [
    {
      icon: 'SL',
      date: '10-02-2024',
      name: 'Silverline Solutions',
      status: 'Active',
    },
    { icon: 'FE', date: '12-02-2024', name: 'FleetEdge', status: 'Inactive' },
  ];

  const currentData = activeTab === 'Super Admins' ? superAdmins : clients;
  const filteredData = currentData.filter(row =>
    row.name.toLowerCase().includes(search.toLowerCase())
  );

  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsModalOpen(false);
      }
    }
    if (isModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isModalOpen]);

  const accessSections = [
    {
      title: 'User & Role Management',
      items: [
        'Create, edit, delete , Dispatchers, Drivers, Users',
        'Assign Admins to taxi companies',
        'Manage permissions for all roles',
        'Suspend/reactivate any user',
      ],
    },
    {
      title: 'Booking & Dispatch Control',
      items: [
        'View, edit, and manage all bookings',
        'Assign/reassign drivers to rides',
        'View driver live locations',
        'Cancel or reschedule any ride',
        'View driver live locations',
      ],
    },
    {
      title: 'Company & Financial Management',
      items: [
        'Add, edit, or remove taxi companies',
        'Manage company subscription plans',
        'View & modify company-specific pricing and fare structures',
        'Access & edit company billing information',
      ],
    },
    {
      title: 'System & Policy Settings',
      items: [
        'Define platform-wide fare policies',
        'Set geofencing rules & restrictions',
        'Control global discount and promo policies',
        'Configure ride cancellation policies',
      ],
    },
    {
      title: 'Reporting & Analytics',
      items: [
        'View and export reports on revenue, ride activity, and system performance',
        'Monitor driver performance & customer ratings',
        'Analyze dispatcher efficiency',
      ],
    },
  ];

  return (
    <div className="p-6">
      <div className="flex items-center justify-end">
        <button
          onClick={() => setIsModalOpen(true)}
          className="flex items-center gap-2 rounded-full bg-[#3324E3] px-4 py-2 text-xs font-medium text-white sm:text-sm"
        >
          <HiPlus /> New Dispatcher
        </button>
      </div>

      {/* Table */}
      <div className="mt-6 overflow-visible rounded-xl border border-gray-200 bg-white">
        <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
          <form className="max-w-md flex-1">
            <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Search
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                <IoIosSearch size={20} />
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                className="block w-3/4 rounded-full border border-0 border-gray-300 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              />
            </div>
          </form>

          <div className="flex items-center gap-2">
            <button
              type="button"
              className="flex items-center gap-1.5 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#13BB76] hover:bg-gray-100 focus:outline-none"
            >
              <span className="h-2 w-2 rounded-full bg-[#13BB76]"></span>
              Active
            </button>

            {/* Inactive Button */}
            <button
              type="button"
              className="flex items-center gap-1.5 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 focus:outline-none"
            >
              <span className="h-2 w-2 rounded-full bg-[#76787A]"></span>
              Inactive
            </button>
            <div
              className="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                id="headlessui-menu-button-r2"
                type="button"
                aria-haspopup="menu"
                aria-expanded="false"
                data-headlessui-state=""
                className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <FiFilter size={18} />
                Filters
              </button>
            </div>

            <button
              type="button"
              aria-label="clock"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <RxCounterClockwiseClock size={22} />
            </button>

            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <FiDownload size={16} />
            </button>
          </div>
        </div>
        <div className="custom-scrollbar max-w-full overflow-x-auto">
          <div className="max-w-[992px] min-w-[-webkit-fill-available]">
            <table className="undefined min-w-full table-auto text-left text-[11px]">
              <thead className="text-[#76787A]">
                <tr>
                  <th className="px-4 py-3 font-medium">Date</th>
                  <th className="px-4 py-3 font-medium">User Name</th>
                  <th className="px-4 py-3 font-medium">Role Name</th>
                  <th className="px-4 py-3 font-medium">Dispatch Control</th>
                  <th className="px-4 py-3 font-medium">
                    Financial Management
                  </th>
                  <th className="px-4 py-3 font-medium">Global Policies</th>
                  <th className="px-4 py-3 font-medium">Status</th>
                  <th className="px-4 py-3"></th>
                </tr>
              </thead>

              <tbody className="divide-y divide-gray-100">
                {filteredData.map((row, index) => (
                  <tr key={index} className="hover:bg-[#E4FFF4]">
                    <td className="px-4 py-3 text-[#050013]">{row.date}</td>
                    <td className="cursor-pointer px-4 py-3 font-medium text-[#050013]">
                      {row.name}
                    </td>
                    <td className="px-4 py-3 text-[#050013]">Super Admin</td>
                    <td className="px-4 py-3 text-[#050013]">Full Access</td>
                    <td className="px-4 py-3 text-[#050013]">Full Access</td>
                    <td className="px-4 py-3 text-[#050013]">Full Access</td>
                    <td className="px-4 py-3">
                      <span
                        className={`flex items-center gap-1 ${
                          row.status === 'Active'
                            ? 'text-[#13BB76]'
                            : 'text-[#8C8B9F]'
                        }`}
                      >
                        <span className="h-[6px] w-[6px] rounded-full bg-current"></span>
                        {row.status}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex justify-center gap-3">
                        <LuPencil className="h-4 w-4 cursor-pointer text-[#050013]" />
                        <FiTrash2 className="h-4 w-4 cursor-pointer text-[#050013]" />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* add new super admin modal */}
      {isModalOpen && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div className="absolute inset-0 bg-black/30"></div>
          <div
            ref={modalRef}
            className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out"
          >
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                New Dispatcher
              </h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <form className="flex flex-col gap-6">
              <div className="px-6 py-3">
                <h3 className="mb-2 text-[14px] font-medium text-[#050013]">
                  Basic Information
                </h3>
                <input
                  type="text"
                  placeholder="Full Name"
                  className="mb-3 w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="email"
                  placeholder="Email ID"
                  className="w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="px-6 py-3">
                <h3 className="mb-4 text-[14px] font-medium text-[#050013]">
                  Access Control and Permissions
                </h3>
                {accessSections.map((section, idx) => (
                  <AccordionSection
                    key={idx}
                    title={section.title}
                    items={section.items}
                  />
                ))}
              </div>

              <div className="sticky bottom-0 border-t bg-white px-6 py-3">
                <button
                  type="submit"
                  className="float-right rounded-full bg-[#3707EF] px-6 py-2 text-white hover:bg-[#3d0cc0]"
                >
                  Add
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
