import React, { Fragment } from 'react';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from '@headlessui/react';
import { RxCross2 } from 'react-icons/rx';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import { HiOutlineTrash } from 'react-icons/hi2';
import { EditIcon, Cloud } from '@/icons';
import { IoMdClose } from 'react-icons/io';
import { GrDocumentDownload } from 'react-icons/gr';

type ApproveDocumentProps = {
  setOpen: (open: boolean) => void;
  open: boolean;
};
export function ApproveDocument(props: ApproveDocumentProps) {
  const { setOpen, open } = props;

  function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
  }

  const tabs = [
    { name: 'Pending', count: 3 },
    { name: 'Approve', count: 2 },
    { name: 'Rejected', count: 1 },
  ];
  return (
    <Dialog
      open={true}
      onClose={setOpen}
      className="relative z-100000"
      style={{ borderTopLeftRadius: '50px' }}
    >
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-[#2a2a2a] opacity-60 transition-opacity duration-500 ease-in-out data-closed:opacity-0"
      />
      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
            <DialogPanel
              transition
              style={{ borderTopLeftRadius: '50px !important' }}
              className="pointer-events-auto relative w-full max-w-4xl transform rounded-tl-[50px] transition duration-500 ease-in-out data-closed:translate-x-full sm:w-[500px] sm:duration-700 md:w-[700px] lg:w-[500px] xl:w-[700px]"
            >
              <div className="flex h-full flex-col overflow-y-scroll rounded-2xl bg-white shadow-xl">
                <div className="bg-tables py-6 pr-[5%] pl-[5%] sm:px-6">
                  <DialogTitle className="flex justify-between text-base font-bold text-gray-900">
                    <div>Approve Document for Emily Davis</div>{' '}
                    <IoMdClose
                      className="text-[#76787A] hover:text-[#3324E3]"
                      size={20}
                    />
                  </DialogTitle>
                </div>
                <div className="relative flex-1 pr-[5%] pl-[5%]">
                  <div>
                    <div className="mt-5 sm:mt-8">
                      <p className="text-dark-grey text-sm dark:text-white">
                        Verification Date{' '}
                        <span className="text-blue-6 font-semibold">
                          26 - 12 -2024
                        </span>
                      </p>
                      <p className="mt-2 text-sm font-semibold text-[#050013] dark:text-white">
                        Upload New Document
                      </p>
                      <div className="mt-3 rounded-lg border border-gray-200 bg-white p-4 shadow-none dark:border-gray-700 dark:bg-gray-800">
                        <div className="flex w-full items-center justify-center">
                          <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-8">
                            <div className="flex items-center justify-center pt-5 pb-6">
                              <Cloud />
                              {/* <svg
                                                                className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                                                                aria-hidden="true"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                fill="none" viewBox="0 0 20 16">
                                                                <path stroke="currentColor"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round"
                                                                    stroke-width="2"
                                                                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                                            </svg> */}
                              <p className="text-dark-grey text-sm dark:text-gray-400">
                                <span className="px-3">
                                  Click or drag file to this area to upload
                                </span>
                              </p>
                            </div>
                            <input
                              id="dropzone-file"
                              type="file"
                              className="hidden"
                            />
                          </label>
                        </div>
                      </div>
                      <p className="text-dark-grey mt-2 text-sm">
                        Formats accepted are PNG & JPG
                      </p>
                      <p className="mt-2 text-[13px] text-[#050013]">
                        If you do not have the file can you see the sample below
                      </p>
                      <div
                        className="bg-tables mt-3 mb-6 flex w-full items-center justify-between p-3"
                        style={{ borderRadius: '10px' }}
                      >
                        <div>
                          <p className="mb-2 text-sm font-semibold text-[#050013]">
                            Sample Certificate
                          </p>
                          <p className="text-sm text-gray-400">PNG 1.2MB</p>
                        </div>
                        <div className="flex items-center gap-2 font-semibold text-blue-800">
                          {' '}
                          {/* Group icon & text */}
                          <GrDocumentDownload className="h-[20px] w-[20px]" />
                          <p className="text-sm">Download</p>
                        </div>
                      </div>
                      {/* <div className='h-[0.5px] w-full bg-[#76787A] my-6 rounded-2xl'></div> */}

                      <div className="border-grey-700 flex justify-between border-t pt-6">
                        <p className="text-sm font-medium font-semibold text-gray-900 dark:text-white">
                          Uploaded Status{' '}
                        </p>
                        <button
                          type="button"
                          className="btn-border flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth="1.5"
                            stroke="currentColor"
                            className="h-4 w-4"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                            />
                          </svg>
                          Filters
                        </button>
                      </div>

                      <div className="flex gap-2">
                        <button
                          type="button"
                          className="btn-border text-blue-6 flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2 text-sm font-medium text-blue-700 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                        >
                          Notify
                        </button>
                        <button
                          type="button"
                          className="btn-border text-blue-6 flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2 text-sm font-medium text-blue-700 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                        >
                          Export
                        </button>
                        <button
                          type="button"
                          className="btn-border text-blue-6 flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2 text-sm font-medium text-blue-700 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                        >
                          Re-upload
                        </button>
                      </div>

                      <TabGroup className="mt-5 p-4">
                        <TabList
                          as="aside"
                          className="flex gap-4 border-b border-gray-200"
                        >
                          {tabs.map((tab, idx) => (
                            <Tab as={Fragment} key={tab.name}>
                              {({ selected }) => (
                                <button
                                  className={classNames(
                                    'relative px-4 py-2 font-medium outline-none',
                                    selected
                                      ? 'relative top-[1px] h-[41px] rounded-t-lg border border-b-0 border-gray-300 bg-white text-blue-700'
                                      : 'text-gray-600'
                                  )}
                                >
                                  {tab.name}
                                </button>
                              )}
                            </Tab>
                          ))}

                          {/* <Tab className="flex gap-2 text-sm  data-[selected]:text-[#3324E3]">Pending <p className='p-1 w-[20px] rounded-full bg-[#3324E3] text-[9px] text-[#ffffff]'>03</p></Tab> */}
                          {/* <Tab className="flex gap-2 text-sm data-[selected]:text-[#3324E3]">Approve<p className='p-1 w-[20px] rounded-full bg-[#76787A20] text-[9px] text-[#000000]'>03</p></Tab>
                                                    <Tab className="flex gap-2 text-sm data-[selected]:text-[#3324E3]">Rejected <p className='p-1 w-[20px] rounded-full bg-[#76787A20] text-[9px] text-[#000000]'>03</p></Tab> */}
                        </TabList>
                        <TabPanels
                          as="section"
                          className="rounded-b-[6px] border border-t-0 border-solid border-gray-200"
                        >
                          <TabPanel>
                            <div className="flex items-center justify-between rounded-lg border border-0 border-gray-200 bg-white p-4 shadow-none transition-shadow duration-200 hover:shadow-none">
                              <div className="flex items-center space-x-4">
                                <div className="relative">
                                  <input
                                    type="checkbox"
                                    placeholder="Select"
                                    // checked={document.selected || false}
                                    // onChange={() => onToggleSelect(document.id)}
                                    className="h-5 w-5 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500"
                                  />
                                  {/* {document.selected && (
                                                                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-600 rounded-full flex items-center justify-center">
                                                                            <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                                                                        </div>
                                                                    )} */}
                                </div>

                                <div className="flex h-16 w-12 flex-shrink-0 items-center justify-center rounded-lg border border-gray-200 bg-gray-50">
                                  {/* {getFileIcon(document.type)} */} demo
                                </div>

                                <div className="flex-1">
                                  <h3 className="text-sm font-semibold text-gray-900">
                                    Document
                                  </h3>
                                  <div className="mt-1 flex items-center space-x-2">
                                    <span className="text-xs text-gray-500 uppercase">
                                      pdf
                                    </span>
                                    <span className="text-xs text-gray-400">
                                      •
                                    </span>
                                    <span className="text-xs text-gray-500">
                                      50
                                    </span>
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2">
                                <button
                                  aria-label="clock"
                                  // onClick={() => onEdit(document.id)}
                                  className="rounded-lg p-2 text-gray-400 transition-colors duration-200 hover:bg-gray-50 hover:text-gray-600"
                                >
                                  <EditIcon size={20} />
                                </button>
                                <button
                                  aria-label="clock"
                                  // onClick={() => onDelete(document.id)}
                                  className="rounded-lg p-2 text-gray-400 transition-colors duration-200 hover:bg-red-50 hover:text-red-500"
                                >
                                  <HiOutlineTrash size={20} />
                                </button>
                              </div>
                            </div>
                          </TabPanel>
                          <TabPanel className="p-4">Content 2</TabPanel>
                          <TabPanel className="p-4">Content 3</TabPanel>
                        </TabPanels>
                      </TabGroup>
                    </div>
                  </div>
                </div>
                <div className="border-grey-700 my-6 flex justify-end space-x-3 border-t pt-6 pr-[5%] pl-[5%]">
                  <button
                    type="button"
                    className="btn-border me-[20px] mb-2 flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2 text-sm font-medium text-blue-700 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                  >
                    Cancel
                  </button>
                  {/* <button
                                             type="button"
                                             className="flex items-center gap-2 py-2 px-5 text-sm font-medium hover:text-gray-900 focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 me-2 mb-2 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                                              Reverify
                                          </button> */}
                  <button
                    type="button"
                    className="bg-cstm-blue-700 me-2 mb-2 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                  >
                    Continue
                  </button>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </div>
    </Dialog>
  );
}
