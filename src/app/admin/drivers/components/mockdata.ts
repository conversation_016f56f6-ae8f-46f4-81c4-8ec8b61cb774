type Document = {
  id: string;
  name: string;
  type: string;
  size: string;
  status: string;
  selected: boolean;
  uploadedAt: Date;
};

export const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'Insurance Copy',
    type: 'png',
    size: '12MB',
    status: 'pending',
    selected: true,
    uploadedAt: new Date('2024-01-15'),
  },
  {
    id: '2',
    name: 'Vehicle Registration',
    type: 'png',
    size: '12MB',
    status: 'pending',
    selected: false,
    uploadedAt: new Date('2024-01-14'),
  },
  {
    id: '3',
    name: 'Work Permit',
    type: 'png',
    size: '12MB',
    status: 'pending',
    selected: false,
    uploadedAt: new Date('2024-01-13'),
  },
  {
    id: '4',
    name: 'Driver License',
    type: 'pdf',
    size: '8MB',
    status: 'approved',
    selected: false,
    uploadedAt: new Date('2024-01-12'),
  },
  {
    id: '5',
    name: 'Passport Copy',
    type: 'pdf',
    size: '15MB',
    status: 'approved',
    selected: false,
    uploadedAt: new Date('2024-01-11'),
  },
  {
    id: '6',
    name: 'Bank Statement',
    type: 'pdf',
    size: '5MB',
    status: 'rejected',
    selected: false,
    uploadedAt: new Date('2024-01-10'),
  },
];
