'use client';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import {
  PiClockCounterClockwiseLight,
  PiDotsThreeVerticalBold,
} from 'react-icons/pi';
import { StarIcon } from '@/icons';
import { FaStar, FaFilter, FaDownload } from 'react-icons/fa';
import { fetchDriverActivityLogs } from '../state/queries';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import Badge from '@/components/ui/badge/Badge';

interface Trip {
  tripId: number;
  dateTime: string;
  startLocation: string;
  endLocation: string;
  status: string;
  distance: number;
  duration: number;
  earnings: number;
  rating: number;
  feedback: string;
  driver: {
    id: number;
    name: string;
    email: string;
  };
}

export default function DriverActivityTable({
  driverDetails,
}: {
  driverDetails: any;
}) {
  const [selectedTrips, setSelectedTrips] = useState<number[]>([]);
  const driverId = driverDetails?.id?.toString();
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  const {
    data: trips = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['driverTrips', driverId],
    queryFn: () => {
      if (!driverId) throw new Error('Driver ID is missing');
      return fetchDriverActivityLogs(driverId);
    },
    enabled: !!driverId, // only runs when driverId is present
  });

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  const SortIcon = ({
    direction,
    active,
  }: {
    direction: 'asc' | 'desc';
    active: boolean;
  }) => {
    let icon: string;
    if (!active) {
      icon = '⇅';
    } else {
      icon = direction === 'asc' ? '↑' : '↓';
    }

    return (
      <span
        className={`ml-1 inline text-[13px] ${
          active ? 'text-gray-700' : 'text-gray-400'
        } cursor-pointer transition-colors hover:text-gray-600`}
      >
        {icon}
      </span>
    );
  };

  const handleSort = (key: keyof Trip) => {
    setSortConfig(prev => {
      if (prev?.key === key) {
        return {
          key,
          direction: prev.direction === 'asc' ? 'desc' : 'asc',
        };
      }
      return { key, direction: 'asc' };
    });
  };

  const sortedTrips = useMemo(() => {
    if (!sortConfig) return trips;

    const sorted = [...trips].sort((a, b) => {
      const valA = a[sortConfig.key];
      const valB = b[sortConfig.key];

      if (valA == null) return 1;
      if (valB == null) return -1;

      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortConfig.direction === 'asc'
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }

      if (typeof valA === 'number' && typeof valB === 'number') {
        return sortConfig.direction === 'asc' ? valA - valB : valB - valA;
      }

      if (valA instanceof Date && valB instanceof Date) {
        return sortConfig.direction === 'asc'
          ? valA.getTime() - valB.getTime()
          : valB.getTime() - valA.getTime();
      }

      return 0;
    });

    return sorted;
  }, [trips, sortConfig]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      case 'in progress':
        return 'warning';
      default:
        return 'primary';
    }
  };

  const handleSelectTrip = (tripId: number) => {
    setSelectedTrips(prev =>
      prev.includes(tripId)
        ? prev.filter(id => id !== tripId)
        : [...prev, tripId]
    );
  };

  const handleSelectAll = () => {
    if (selectedTrips.length === trips.length) {
      setSelectedTrips([]);
    } else {
      setSelectedTrips(trips.map(trip => trip.tripId));
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        Loading trips...
      </div>
    );
  }

  if (isError) {
    return <div className="p-4 text-red-500">Error: {error.message}</div>;
  }

  if (trips.length === 0) {
    return (
      <div className="p-4 text-blue-500">No trips found for this driver.</div>
    );
  }

  return (
    <div className="tabled">
      <div className="overflow-hidden rounded-b-[12px] bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="header-bar bg-tables flex items-center justify-between p-3 dark:bg-gray-800">
          <div className="flex items-center space-x-4"></div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                />
              </svg>
              Filters
            </button>
            <button
              type="button"
              aria-label="refresh"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <PiClockCounterClockwiseLight size={22} />
            </button>
            <button
              type="button"
              aria-label="download"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="size-4"
              >
                <path
                  fillRule="evenodd"
                  d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                  clip-rule="evenodd"
                />
              </svg>
              {/* <FaDownload size={20} /> */}
            </button>
          </div>
        </div>
        <div className="custom-scrollbar max-w-full overflow-x-auto">
          <div className="max-w-[900px] min-w-[-webkit-fill-available]">
            <Table>
              <TableHeader className="bg-tables border-b dark:border-white/[0.05]">
                <TableRow className="border-t">
                  <TableCell
                    isHeader
                    className="w-10 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-400"
                  >
                    <input
                      type="checkbox"
                      checked={
                        selectedTrips.length === trips.length &&
                        trips.length > 0
                      }
                      onChange={handleSelectAll}
                      className="form-checkbox h-4 w-4 rounded text-blue-600"
                    />
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('tripId')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Trip ID
                      <SortIcon
                        direction={
                          sortConfig?.key === 'tripId'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'tripId'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('dateTime')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Date/Time
                      <SortIcon
                        direction={
                          sortConfig?.key === 'dateTime'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'dateTime'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('startLocation')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Start Location
                      <SortIcon
                        direction={
                          sortConfig?.key === 'startLocation'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'startLocation'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('endLocation')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      End Location
                      <SortIcon
                        direction={
                          sortConfig?.key === 'endLocation'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'endLocation'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('status')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Status
                      <SortIcon
                        direction={
                          sortConfig?.key === 'status'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'status'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('distance')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Distance
                      <SortIcon
                        direction={
                          sortConfig?.key === 'distance'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'distance'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('duration')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Duration
                      <SortIcon
                        direction={
                          sortConfig?.key === 'duration'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'duration'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('earnings')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Earnings
                      <SortIcon
                        direction={
                          sortConfig?.key === 'earnings'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'earnings'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <div
                      onClick={() => handleSort('rating')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                    >
                      Ratings
                      <SortIcon
                        direction={
                          sortConfig?.key === 'rating'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'rating'}
                      />
                    </div>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    Feedback
                  </TableCell>
                  {/* <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                    children={''}
                  ></TableCell> */}
                </TableRow>
              </TableHeader>
              <TableBody className="bg-tables divide-y dark:divide-white/[0.05]">
                {sortedTrips.map(trip => (
                  <TableRow
                    key={trip.tripId}
                    className="dark:hover:bg-gray-800"
                  >
                    <TableCell className="w-10 px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedTrips.includes(trip.tripId)}
                        onChange={() => handleSelectTrip(trip.tripId)}
                        className="form-checkbox h-4 w-4 rounded text-blue-600"
                      />
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs font-medium whitespace-nowrap text-[#050013]">
                        #{String(trip.tripId).padStart(5, '0')}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs whitespace-nowrap text-[#050013]">
                        {formatDateTime(trip.dateTime)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs whitespace-nowrap text-[#050013]">
                        {trip.startLocation}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs whitespace-nowrap text-[#050013]">
                        {trip.endLocation}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <Badge size="sm" color={getStatusColor(trip.status)}>
                        <span className="capitalize">{trip.status}</span>
                      </Badge>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs whitespace-nowrap text-[#050013]">
                        {trip.distance.toFixed(2)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs whitespace-nowrap text-[#050013]">
                        {formatDuration(trip.duration)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs whitespace-nowrap text-[#050013]">
                        ${trip.earnings.toFixed(2)}
                      </p>
                    </TableCell>

                    <TableCell className="px-4 py-3">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {trip?.rating} <StarIcon />
                      </p>
                    </TableCell>
                    <TableCell className="max-w-[200px] px-4 py-3">
                      <p className="line-clamp-2 text-xs text-[#050013]">
                        {trip.feedback}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <Tippy
                        content={
                          <div className="bg-white text-gray-900">
                            <div className="flex flex-col space-y-1 p-1">
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                View Trip Details
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Show Feedback
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Report an Issue
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Resolve Payment
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Mark as Reviewed
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Export Trip Report
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Send Reminder To Driver
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Add Tags to Trip
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Download Trip Receipt
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Reassign Trip
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Delete Trip
                              </button>
                            </div>
                          </div>
                        }
                        interactive={true}
                        placement="left"
                        theme="light"
                        arrow={false}
                        duration={0}
                        className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                      >
                        <button
                          type="button"
                          className="focus:outline-none"
                          aria-label="actions"
                        >
                          <PiDotsThreeVerticalBold size={20} />
                        </button>
                      </Tippy>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}
