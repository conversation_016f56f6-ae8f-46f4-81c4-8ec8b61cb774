'use client';

import { Outfit } from 'next/font/google';
import './globals.css';
import { SidebarProvider } from '@/context/SidebarContext';
import { ThemeProvider } from '@/context/ThemeContext';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import SignIn from '@/app/(full-width-pages)/(auth)/signin/page';
import { GlobalContextProvider } from '@/hooks/useGlobalContext';
import { ToastContainer } from 'react-toastify';
import { SideMenuBottom } from '@/icons';
import 'react-toastify/dist/ReactToastify.css';
const outfit = Outfit({
  variable: '--font-outfit-sans',
  subsets: ['latin'],
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(false);

  useEffect(() => {
    const loggedIn = localStorage.getItem('isLoggedIn');
    if (loggedIn) {
      setIsLoggedIn(true);
    }
  }, []);
  return (
    <html lang="en">
      <body className={`${outfit.variable} dark:bg-gray-900`}>
        <GlobalContextProvider>
          <ToastContainer
            position="top-center"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick={false}
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
          <ThemeProvider>
            <SidebarProvider>{children}</SidebarProvider>
          </ThemeProvider>
          {/* {isLoggedIn ? (
            <ThemeProvider>
              <SidebarProvider>{children}</SidebarProvider>
            </ThemeProvider>
          ) : (
            pathName !== "/super-admin/login" && (
              <div className="relative z-1 bg-white p-6 sm:p-0">
                <div className="relative flex h-screen w-full flex-col justify-center sm:p-0 lg:flex-row">
                  <div className="relative hidden h-full w-full items-center lg:grid lg:w-1/2 dark:bg-white/5">
                    <Image
                      className="h-screen w-full"
                      width={100}
                      height={100}
                      src="/images/bg-image/login_bg.png"
                      alt="background-image"
                    />
                    <Image
                      className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform"
                      width={219}
                      height={48}
                      src="/images/logo/auth-logo.png"
                      alt="logo-image"
                    />
                  </div>
                  <SignIn />
                </div>
              </div>
            )
          )} */}
        </GlobalContextProvider>
      </body>
    </html>
  );
}
