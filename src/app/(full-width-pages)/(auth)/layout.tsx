import Image from 'next/image';
import React from 'react';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative z-1 bg-white p-6 sm:p-0">
      <div className="relative flex h-screen w-full flex-col justify-center sm:p-0 lg:flex-row">
        <div
          className="bg-brand-950 hidden h-full w-full items-center lg:grid lg:w-1/2 dark:bg-white/5"
          style={{
            background:
              'linear-gradient(rgb(24, 236, 149), rgb(37, 138, 187), rgb(55, 7, 239), rgb(112, 24, 235))',
          }}
        >
          <div className="relative z-1 flex items-center justify-center">
            <div className="flex max-w-xs flex-col items-center">
              <Image
                width={231}
                height={48}
                src="/images/logo/auth-logo.png"
                alt="Logo"
              />
            </div>
          </div>
        </div>
        {children}
      </div>
    </div>
  );
}
