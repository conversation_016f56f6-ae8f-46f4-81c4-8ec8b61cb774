'use client';
import { useState } from 'react';
import VehiclesTable from './vehiclesTable';
import ScheduleMaintain from './scheduleMaintain';
import VehicleType from './vehicleType';
import VehicleMake from './vehicleMake';
import VehicleModel from './vehicleModel';

type IconType = any;

const getIcon = (iconType: IconType) => {
  if (iconType === 'register') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        className="size-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
        />
      </svg>
    );
  } else if (iconType === 'scheduleMaintain') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        className="size-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
        />
      </svg>
    );
  } else if (iconType === 'vehicleType') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        className="size-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
        />
      </svg>
    );
  } else if (iconType === 'vehicleMake') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        className="size-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
        />
      </svg>
    );
  } else if (iconType === 'vehicleModel') {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        className="size-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
        />
      </svg>
    );
  }
  return null;
};

export default function VehicleMaster() {
  const [activeTab, setActiveTab] = useState('register');
  const [showTabs, setShowTabs] = useState(true);

  const tabs = [
    { id: 'register', label: 'Register Vehicle', iconType: 'register' },
    {
      id: 'scheduleMaintain',
      label: 'Maintenance Schedule',
      iconType: 'scheduleMaintain',
    },
    { id: 'vehicleType', label: 'Vehicle Type', iconType: 'vehicleType' },
    { id: 'vehicleMake', label: 'Vehicle Make', iconType: 'vehicleMake' },
    { id: 'vehicleModel', label: 'Vehicle Model', iconType: 'vehicleModel' },
  ];

  return (
    <div>
      {showTabs && (
        <div className="mb-4">
          <ul className="-mb-px flex flex-wrap text-center text-sm font-medium">
            {tabs.map(({ id, label, iconType }) => (
              <li className="me-2" role="presentation" key={id}>
                <button
                  className={`inline-flex items-center gap-2 rounded-t-lg border-b-2 p-4 ${
                    activeTab === id
                      ? 'border-blue-800 text-blue-800'
                      : 'hover:border-gray-300 hover:text-gray-600 dark:hover:text-gray-300'
                  }`}
                  onClick={() => setActiveTab(id)}
                  role="tab"
                >
                  {iconType && getIcon(iconType)}
                  {label}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
      <div>
        {activeTab === 'register' && (
          <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <VehiclesTable setShowTabs={setShowTabs} />
          </div>
        )}
        {activeTab === 'scheduleMaintain' && (
          <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <ScheduleMaintain />
          </div>
        )}
        {activeTab === 'vehicleType' && (
          <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <VehicleType />
          </div>
        )}
        {activeTab === 'vehicleMake' && (
          <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <VehicleMake />
          </div>
        )}
        {activeTab === 'vehicleModel' && (
          <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <VehicleModel />
          </div>
        )}
      </div>
    </div>
  );
}
