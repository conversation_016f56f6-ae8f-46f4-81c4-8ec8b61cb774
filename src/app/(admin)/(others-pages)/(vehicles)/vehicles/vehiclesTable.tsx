'use client';
import React, { Di<PERSON>atch, SetStateAction } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  TransitionChild,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Badge from '../../../../../components/ui/badge/Badge';
import Select from '../../../../../components/form/Select';
import Input from '../../../../../components/form/input/InputField';
import ChevronDownIcon from '../../../../../icons/chevron-down.svg';
import { initFlowbite } from 'flowbite';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import { PiCar, PiClipboard } from 'react-icons/pi';

interface VehiclesTableProps {
  setShowTabs: Dispatch<SetStateAction<boolean>>;
}

interface Order {
  id: number;
  user: {
    image: string;
    name: string;
    role: string;
  };
  projectName: string;
  team: {
    images: string[];
  };
  status: string;
  budget: string;
}

// Define the table data using the interface
const tableData: Order[] = [
  {
    id: 1,
    user: {
      image: '/images/user/user-17.jpg',
      name: 'Lindsey Curtis',
      role: 'Web Designer',
    },
    projectName: 'Agency Website',
    team: {
      images: [
        '/images/user/user-22.jpg',
        '/images/user/user-23.jpg',
        '/images/user/user-24.jpg',
      ],
    },
    budget: '3.9K',
    status: 'Active',
  },
  {
    id: 2,
    user: {
      image: '/images/user/user-18.jpg',
      name: 'Kaiya George',
      role: 'Project Manager',
    },
    projectName: 'Technology',
    team: {
      images: ['/images/user/user-25.jpg', '/images/user/user-26.jpg'],
    },
    budget: '24.9K',
    status: 'Pending',
  },
  {
    id: 3,
    user: {
      image: '/images/user/user-17.jpg',
      name: 'Zain Geidt',
      role: 'Content Writing',
    },
    projectName: 'Blog Writing',
    team: {
      images: ['/images/user/user-27.jpg'],
    },
    budget: '12.7K',
    status: 'Active',
  },
  {
    id: 4,
    user: {
      image: '/images/user/user-20.jpg',
      name: 'Abram Schleifer',
      role: 'Digital Marketer',
    },
    projectName: 'Social Media',
    team: {
      images: [
        '/images/user/user-28.jpg',
        '/images/user/user-29.jpg',
        '/images/user/user-30.jpg',
      ],
    },
    budget: '2.8K',
    status: 'Cancel',
  },
  {
    id: 5,
    user: {
      image: '/images/user/user-21.jpg',
      name: 'Carla George',
      role: 'Front-end Developer',
    },
    projectName: 'Website',
    team: {
      images: [
        '/images/user/user-31.jpg',
        '/images/user/user-32.jpg',
        '/images/user/user-33.jpg',
      ],
    },
    budget: '4.5K',
    status: 'Active',
  },
];

const options = [
  { value: 'marketing', label: 'Marketing' },
  { value: 'template', label: 'Template' },
  { value: 'development', label: 'Development' },
];

const handleSelectChange = (value: string) => {
  console.log('Selected value:', value);
};

export default function VehiclesTable({ setShowTabs }: VehiclesTableProps) {
  const [open, setOpen] = useState(false); // Initially closed
  const [currentStep, setCurrentStep] = useState(1);
  const steps = [
    { icon: PiCar, label: 'Vehicle Details', subtitle: '10 Questions' },
    { icon: PiClipboard, label: 'Upload Document', subtitle: '4 Documents' },
  ];
  const handleNext = () => {
    if (currentStep < steps.length) setCurrentStep(currentStep + 1);
  };
  const handleBack = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };
  const handleReset = () => {
    setCurrentStep(1);
  };
  useEffect(() => {
    initFlowbite();
    const observer = new MutationObserver(() => {
      initFlowbite();
    });
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
    return () => observer.disconnect();
  }, [tableData]); // Add tableData as dependency
  const handleSelectOrder = (id: number) => {
    console.log(`Selected order ID: ${id}`);
    // Perform any other action, like updating state or dispatching an event.
  };
  const [selectedOrder, setSelectedOrder] = useState(null);
  const handleRowClick = (order: any) => {
    setSelectedOrder(order);
    setShowTabs(false); // Show tabs on row click
  };
  const handleGoBack = () => {
    setSelectedOrder(null);
    setShowTabs(true); // Hide tabs when going back
  };
  // @ts-ignore
  return (
    <>
      {!selectedOrder ? (
        <div className="tabled">
          <div className="flex justify-end">
            <button
              onClick={() => setOpen(true)}
              type="button"
              className="me-2 mb-2 flex items-center gap-2 rounded-full bg-blue-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="h-5 w-5"
              >
                <path
                  fillRule="evenodd"
                  d="M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z"
                  clipRule="evenodd"
                />
              </svg>
              Register New Vehicle
            </button>
          </div>
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="header-bar flex items-center justify-between bg-gray-100 p-3 dark:bg-gray-800">
              {/* Search Bar */}
              <form className="max-w-md flex-1">
                <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  Search
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                    <svg
                      className="h-4 w-4 text-gray-500 dark:text-gray-400"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 20 20"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                      />
                    </svg>
                  </div>
                  <input
                    type="search"
                    id="default-search"
                    className="block w-3/4 rounded-full border border-gray-300 bg-gray-50 p-2 ps-10 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                    placeholder="Search Here"
                  />
                </div>
              </form>
              {/* Buttons Container */}
              <div className="flex items-center gap-2">
                {/* Filter Button */}
                <button
                  type="button"
                  className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className="h-4 w-4"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                    />
                  </svg>
                  Filters
                </button>
                {/* Refresh Button */}
                <button
                  type="button"
                  className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-gray-900 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="h-5 w-5"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                {/* Download Button */}
                <button
                  type="button"
                  className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-gray-900 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="size-6"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="max-w-full overflow-x-auto">
              <div className="min-w-[1102px]">
                <Table>
                  {/* Table Header */}
                  <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                    <TableRow>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Select
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Type
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Model
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Fuel Type
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Licence Plate
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Verification
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Owner
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Insurance Status
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs px-5 py-3 text-start font-medium text-gray-500 dark:text-gray-400"
                      >
                        Date
                      </TableCell>
                      <TableCell
                        isHeader
                        className="text-theme-xs py-3 text-right font-medium text-gray-500 dark:text-gray-400"
                      >
                        &nbsp;
                      </TableCell>
                    </TableRow>
                  </TableHeader>
                  {/* Table Body */}
                  <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                    {tableData.map((order, index) => (
                      <TableRow
                        key={index}
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
                      >
                        <TableCell className="px-5 py-2 text-start sm:px-6">
                          <input
                            type="checkbox"
                            name="selectOrder" // Optionally, group checkboxes by giving the same name
                            value={order.id}
                            className="form-checkbox text-blue-500"
                            onChange={() => handleSelectOrder(order.id)} // Handle the checkbox selection change
                          />
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                          <p>test</p>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                          <p onClick={() => handleRowClick(order)}>test</p>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                          <p>test</p>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-gray-500 dark:text-gray-400">
                          <Badge
                            size="sm"
                            color={
                              order.status === 'Active'
                                ? 'success'
                                : order.status === 'Pending'
                                  ? 'warning'
                                  : 'error'
                            }
                          >
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-gray-500 dark:text-gray-400">
                          <p>test</p>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-gray-500 dark:text-gray-400">
                          <Badge
                            size="sm"
                            color={
                              order.status === 'Active'
                                ? 'success'
                                : order.status === 'Pending'
                                  ? 'warning'
                                  : 'error'
                            }
                          >
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 py-3 text-gray-500 dark:text-gray-400">
                          <p>test</p>
                        </TableCell>
                        <TableCell className="text-theme-sm px-6 py-3 text-gray-500 dark:text-gray-400">
                          <p>2012-01-02</p>
                        </TableCell>
                        <TableCell className="text-theme-sm px-4 text-gray-500 dark:text-gray-400">
                          <Tippy
                            content={
                              <div className="bg-white text-gray-900">
                                <div className="flex flex-col space-y-1 p-1">
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Add New Record
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Edit Record
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Duplicate Record
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Delete Document
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    View Cost Analysis
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Send Notification
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Edit Details
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Merge Records
                                  </button>
                                  <button className="w-full rounded px-2 py-1 text-left text-start hover:bg-gray-100">
                                    Download
                                  </button>
                                </div>
                              </div>
                            }
                            interactive={true}
                            placement="right"
                            theme="light"
                            arrow={false}
                            duration={0}
                            className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                          >
                            <button
                              type="button"
                              className="focus:outline-none"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                                className="size-6"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </button>
                          </Tippy>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            <Dialog
              open={open}
              onClose={setOpen}
              className="relative z-100000"
              style={{ borderTopLeftRadius: '50px' }}
            >
              <DialogBackdrop
                transition
                className="fixed inset-0 bg-gray-500/75 transition-opacity duration-500 ease-in-out data-closed:opacity-0"
              />
              <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 overflow-hidden">
                  <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                    <DialogPanel
                      transition
                      style={{ borderTopLeftRadius: '50px !important' }}
                      className="pointer-events-auto relative w-full w-screen max-w-4xl transform rounded-tl-[50px] transition duration-500 ease-in-out data-closed:translate-x-full sm:w-[500px] sm:duration-700 md:w-[700px] lg:w-[500px] xl:w-[700px]"
                    >
                      <TransitionChild>
                        <div className="absolute top-0 left-0 -ml-8 flex pt-4 pr-2 duration-500 ease-in-out data-closed:opacity-0 sm:-ml-10 sm:pr-4">
                          <button
                            type="button"
                            onClick={() => setOpen(false)}
                            className="relative rounded-md text-gray-300 hover:text-white focus:ring-2 focus:ring-white focus:outline-hidden"
                          >
                            <span className="absolute -inset-2.5" />
                            <span className="sr-only">Close panel</span>
                            <XMarkIcon aria-hidden="true" className="size-6" />
                          </button>
                        </div>
                      </TransitionChild>
                      <div className="flex h-full flex-col overflow-y-scroll bg-white py-6 shadow-xl">
                        <div className="px-4 sm:px-6">
                          <DialogTitle className="text-base font-semibold text-gray-900">
                            Vehicles
                          </DialogTitle>
                        </div>
                        <div className="relative mt-6 flex-1 px-4 sm:px-6">
                          <div>
                            {/* Stepper Navigation */}
                            <ul className="flex w-full items-center">
                              {steps.map((step, index) => {
                                const stepIndex = index + 1;
                                const isActive = currentStep === stepIndex;
                                const IconComponent = step.icon; // Get the icon component dynamically

                                return (
                                  <li
                                    key={stepIndex}
                                    className="flex w-full items-center"
                                  >
                                    {/* Step Icon Container */}
                                    <div className="relative flex items-center">
                                      <div
                                        className={`flex size-12 items-center justify-center rounded-full border ${isActive ? 'border-blue-600' : 'border-gray-300'}`}
                                      >
                                        <IconComponent
                                          size={24}
                                          className={`${isActive ? 'text-blue-600' : 'text-gray-600'}`}
                                        />
                                      </div>
                                    </div>

                                    {/* Step Text */}
                                    <div className="ml-2 flex flex-col">
                                      <span
                                        className={`text-sm ${isActive ? 'font-medium text-blue-600' : 'text-gray-900'}`}
                                      >
                                        {step.label}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        {step.subtitle}
                                      </span>
                                    </div>

                                    {/* Step Connector */}
                                    {stepIndex < steps.length && (
                                      <hr className="mx-4 flex-1 border-t border-gray-300" />
                                    )}
                                  </li>
                                );
                              })}
                            </ul>

                            {/* Stepper Content */}
                            {currentStep === 1 && (
                              <div className="mt-5 sm:mt-8">
                                <div className="flex items-center justify-center rounded-xl border-gray-200">
                                  <div className="w-full">
                                    <label
                                      htmlFor="first_name"
                                      className="text-md mb-3 font-black dark:text-white"
                                    >
                                      Overiew
                                    </label>
                                    <div className="mt-3 mb-3">
                                      <div className="relative">
                                        <Select
                                          options={options}
                                          placeholder="Model"
                                          onChange={handleSelectChange}
                                          className="dark:bg-dark-900"
                                        />
                                        <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                          <ChevronDownIcon />
                                        </span>
                                      </div>
                                    </div>
                                    <div className="mb-3 grid grid-cols-2 gap-2">
                                      <div>
                                        <div className="relative">
                                          <Select
                                            options={options}
                                            placeholder="Year"
                                            onChange={handleSelectChange}
                                            className="dark:bg-dark-900 w-100"
                                          />
                                          <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                            <ChevronDownIcon />
                                          </span>
                                        </div>
                                      </div>

                                      <div>
                                        <div className="relative">
                                          <Select
                                            options={options}
                                            placeholder="Make"
                                            onChange={handleSelectChange}
                                            className="dark:bg-dark-900"
                                          />
                                          <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                            <ChevronDownIcon />
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                    <Input
                                      type="text"
                                      placeholder="Registration Number"
                                      className="mb-3"
                                    />
                                    <div className="mb-5 grid grid-cols-2 gap-2">
                                      <div>
                                        <div className="relative">
                                          <Select
                                            options={options}
                                            placeholder="Vehicle Type"
                                            onChange={handleSelectChange}
                                            className="dark:bg-dark-900 w-100"
                                          />
                                          <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                            <ChevronDownIcon />
                                          </span>
                                        </div>
                                      </div>

                                      <div>
                                        <div className="relative">
                                          <Select
                                            options={options}
                                            placeholder="Fuel Type"
                                            onChange={handleSelectChange}
                                            className="dark:bg-dark-900"
                                          />
                                          <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                            <ChevronDownIcon />
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                    <Input
                                      type="text"
                                      placeholder="Vehicle Name"
                                      className="mb-3"
                                    />
                                    <div className="mb-5 grid grid-cols-2 gap-2">
                                      <div>
                                        <div className="relative">
                                          <Select
                                            options={options}
                                            placeholder="Max Bag/ Cases"
                                            onChange={handleSelectChange}
                                            className="dark:bg-dark-900 w-100"
                                          />
                                          <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                            <ChevronDownIcon />
                                          </span>
                                        </div>
                                      </div>

                                      <div>
                                        <div className="relative">
                                          <Select
                                            options={options}
                                            placeholder="Max Passengers"
                                            onChange={handleSelectChange}
                                            className="dark:bg-dark-900"
                                          />
                                          <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                            <ChevronDownIcon />
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="relative mb-3">
                                      <Select
                                        options={options}
                                        placeholder="Features(Wheelchair, Streacher)"
                                        onChange={handleSelectChange}
                                        className="dark:bg-dark-900"
                                      />
                                      <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                                        <ChevronDownIcon />
                                      </span>
                                    </div>
                                    <label
                                      htmlFor="first_name"
                                      className="text-md mb-5 font-black text-gray-900 dark:text-white"
                                    >
                                      Ownership Overview
                                    </label>
                                    <Input
                                      type="text"
                                      placeholder="Owner Name"
                                      className="mt-3 mb-3"
                                    />
                                    <Input
                                      type="text"
                                      placeholder="Lease Ownership Status"
                                      className="mb-3"
                                    />
                                  </div>
                                </div>
                              </div>
                            )}

                            {currentStep === 2 && (
                              <div className="mt-5 sm:mt-8">
                                <label
                                  htmlFor="first_name"
                                  className="text-md font-black text-gray-900 dark:text-white"
                                >
                                  Certificate of Conformity
                                </label>
                                <div className="mt-3 rounded-lg border border-gray-200 bg-white p-1 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                                  <div className="flex w-full items-center justify-center">
                                    <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600 dark:hover:bg-gray-800">
                                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                        <svg
                                          className="mb-4 h-8 w-8 text-gray-500 dark:text-gray-400"
                                          aria-hidden="true"
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 20 16"
                                        >
                                          <path
                                            stroke="currentColor"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                                          />
                                        </svg>
                                        <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                          <span className="font-semibold">
                                            Click od drag file to this area to
                                            upload
                                          </span>
                                        </p>
                                      </div>
                                      <input
                                        id="dropzone-file"
                                        type="file"
                                        className="hidden"
                                      />
                                    </label>
                                  </div>
                                </div>
                                <p className="mt-2 text-sm text-gray-400">
                                  Format are accepted jpg & png
                                </p>
                                <p className="text-dark mt-2 text-sm">
                                  If you do not have the file can you see the
                                  file bellow
                                </p>
                                <div
                                  className="mt-3 flex w-full items-center justify-between bg-gray-200 p-3"
                                  style={{ borderRadius: '10px' }}
                                >
                                  <div>
                                    <p className="text-dark text-sm">
                                      Sample Certificate
                                    </p>
                                    <p className="text-sm text-gray-400">
                                      PNG 1.2MB
                                    </p>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {' '}
                                    {/* Group icon & text */}
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      strokeWidth="1.5"
                                      stroke="currentColor"
                                      className="size-6 text-blue-800"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25"
                                      />
                                    </svg>
                                    <p className="text-sm text-blue-800">
                                      Download
                                    </p>
                                  </div>
                                </div>
                                <div className="mt-3 mb-3 flex items-center justify-end gap-2">
                                  <button
                                    type="button"
                                    className="rounded-lg border border-gray-200 bg-white px-10 py-2.5 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                                    style={{ borderRadius: '50px' }}
                                  >
                                    Cancel
                                  </button>

                                  <button
                                    type="button"
                                    className="flex items-center gap-2 rounded-lg bg-gray-800 px-5 py-2.5 text-sm font-medium text-white hover:bg-gray-900 focus:ring-4 focus:ring-gray-300 focus:outline-none dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                                    style={{ borderRadius: '50px' }}
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      strokeWidth="1.5"
                                      stroke="currentColor"
                                      className="size-4"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                                      />
                                    </svg>
                                    <span>Upload</span>
                                  </button>
                                </div>
                                <label
                                  htmlFor="first_name"
                                  className="text-md font-black text-gray-900 dark:text-white"
                                >
                                  Proof of Roadworthiness: APK
                                </label>
                                <div className="max mt-3 rounded-lg border border-gray-200 bg-white p-1 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                                  <div className="flex w-full items-center justify-center">
                                    <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600 dark:hover:bg-gray-800">
                                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                        <svg
                                          className="mb-4 h-8 w-8 text-gray-500 dark:text-gray-400"
                                          aria-hidden="true"
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 20 16"
                                        >
                                          <path
                                            stroke="currentColor"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                                          />
                                        </svg>
                                        <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                          <span className="font-semibold">
                                            Click od drag file to this area to
                                            upload
                                          </span>
                                        </p>
                                      </div>
                                      <input
                                        id="dropzone-file"
                                        type="file"
                                        className="hidden"
                                      />
                                    </label>
                                  </div>
                                </div>
                                <p className="mt-2 text-sm text-gray-400">
                                  Format are accepted jpg & png
                                </p>
                                <p className="text-dark mt-2 text-sm">
                                  If you do not have the file can you see the
                                  file bellow
                                </p>
                                <div
                                  className="mt-3 flex w-full items-center justify-between bg-gray-200 p-3"
                                  style={{ borderRadius: '10px' }}
                                >
                                  <div>
                                    <p className="text-dark text-sm">
                                      Sample Certificate
                                    </p>
                                    <p className="text-sm text-gray-400">
                                      PNG 1.2MB
                                    </p>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {' '}
                                    {/* Group icon & text */}
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      strokeWidth="1.5"
                                      stroke="currentColor"
                                      className="size-6 text-blue-800"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25"
                                      />
                                    </svg>
                                    <p className="text-sm text-blue-800">
                                      Download
                                    </p>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Button Group */}
                            <div className="mt-5 flex items-center justify-between gap-x-2">
                              <button
                                onClick={handleBack}
                                disabled={currentStep === 1}
                                className="rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-800 hover:bg-gray-50 disabled:opacity-50"
                              >
                                Back
                              </button>
                              {currentStep < steps.length ? (
                                <button
                                  onClick={handleNext}
                                  className="rounded-lg border bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
                                >
                                  Next
                                </button>
                              ) : (
                                <button
                                  onClick={handleReset}
                                  className="rounded-lg border bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
                                >
                                  Reset
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </DialogPanel>
                  </div>
                </div>
              </div>
            </Dialog>
          </div>
        </div>
      ) : (
        <div className="space-y-6 rounded-xl border border-gray-200 bg-gray-100 p-6">
          {/* Card Header */}
          <h2
            className="text-sm font-semibold text-gray-500"
            onClick={handleGoBack}
          >
            {' '}
            ← Go back
          </h2>
          <div className="flex items-center justify-between rounded-lg">
            <div className="flex items-center space-x-4">
              {/* Vehicle Image */}
              <img
                src="/images/vehicles/hyundai-tucson.png"
                alt="Hyundai Tucson"
                className="h-16 w-26 rounded-lg object-cover"
              />
              {/* Vehicle Details */}
              <div>
                <h2 className="text-xl font-semibold text-gray-800">
                  Hyundai Tucson
                </h2>
                <span className="text-sm text-yellow-600">
                  🔶 Pending Verification
                </span>
              </div>
            </div>

            {/* Plate Number Badge */}
            <div className="rounded-lg bg-yellow-500 px-3 py-1 text-sm font-medium text-white">
              00-12568
            </div>
          </div>

          <hr />

          {/* Vehicle Details */}
          <h3 className="text-md mb-3 rounded-2xl bg-white px-3 text-gray-500">
            Vehicle Details
          </h3>
          <div className="rounded-lg p-4">
            <div className="grid grid-cols-4 gap-4 text-sm text-gray-700">
              {[
                { label: 'Model', value: 'Hyundai Tucson' },
                { label: 'Type', value: 'Sedan' },
                { label: 'Plate Number', value: 'EV MT-9826-SA' },
                { label: 'Color', value: 'Black' },
                {
                  label: 'Registration Status',
                  value: 'Pending',
                  className: 'text-yellow-600',
                },
                { label: 'Registration Expiry', value: 'Dec 20, 2025' },
                { label: 'Insurance Expiry', value: 'Jan 15, 2025' },
                { label: 'Renewal Reminder', value: 'ON' },
              ].map((item, index) => (
                <div key={index}>
                  <span className="text-gray-500">{item.label}</span>
                  <br />
                  <span className={`text-xl ${item.className || ''}`}>
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Documents & Verification */}
          <h3 className="text-md mb-3 rounded-2xl bg-white px-3 text-gray-500">
            Documents & Verification
          </h3>
          <div className="rounded-lg p-4">
            <div className="grid grid-cols-4 gap-4 text-sm text-gray-700">
              {[
                { label: "Driver's License", value: '✅ Verified' },
                { label: 'Insurance Copy', value: '✅ Verified' },
                {
                  label: 'Registration Certificate',
                  value: '✅ Verified',
                },
                { label: 'Background Check', value: 'Dec 20, 2025' },
                { label: 'License Expiry', value: 'Jan 20, 2030' },
              ].map((item, index) => (
                <div key={index}>
                  <span className="text-gray-500">{item.label}</span>
                  <br />
                  <span className="text-xl">{item.value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Vehicle Performance */}
          <h3 className="text-md mb-3 rounded-2xl bg-white px-3 text-gray-500">
            Vehicle Performance
          </h3>
          <div className="rounded-lg p-4">
            <div className="grid grid-cols-4 gap-4 text-sm text-gray-700">
              {[
                { label: 'Mileage', value: '25,000 KM' },
                { label: 'Fuel Level', value: '65%' },
              ].map((item, index) => (
                <div key={index}>
                  <span className="text-gray-500">{item.label}</span>
                  <br />
                  <span className="text-xl">{item.value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
