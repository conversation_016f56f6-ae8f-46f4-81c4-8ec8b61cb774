'use client';
import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import Badge from '../../../../../components/ui/badge/Badge';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import {
  useState,
  useEffect,
  useMemo,
  JSXElementConstructor,
  ReactElement,
  ReactNode,
  ReactPortal,
} from 'react';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import { fetchDriverSupportLogs } from '../state/queries';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { FiChevronUp, FiChevronDown } from 'react-icons/fi';
import { useQuery } from '@tanstack/react-query';

interface SupportLog {
  id: number;
  issue_id: string;
  issue_type: string;
  issue_description: string;
  priority: string;
  status: string;
  resolution_date: string | null;
  assigned_admin: string;
  created_at: string;
  driverName: string;
  driverEmail: string;
  driverPhone: string;
  driverImage: string;
  driverStatus: string;
  driverRegion: string;
}

interface DocumentTabProps {
  driverDetails: any;
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'resolved':
      return 'success';
    case 'pending':
      return 'warning';
    case 'cancelled':
      return 'error';
    default:
      return 'primary';
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return 'error';
    case 'medium':
      return 'warning';
    case 'low':
      return 'success';
    default:
      return 'primary';
  }
};

const SortIcon = ({
  direction,
  active,
}: {
  direction: 'asc' | 'desc';
  active: boolean;
}) => {
  let icon:
    | string
    | number
    | bigint
    | boolean
    | ReactElement<unknown, string | JSXElementConstructor<any>>
    | Iterable<ReactNode>
    | Promise<
        | string
        | number
        | bigint
        | boolean
        | ReactPortal
        | ReactElement<unknown, string | JSXElementConstructor<any>>
        | Iterable<ReactNode>
      >;

  if (!active) {
    icon = '⇅';
  } else {
    icon = direction === 'asc' ? '↑' : '↓';
  }

  return (
    <span
      className={`ml-1 inline text-[13px] ${
        active ? 'text-gray-700' : 'text-gray-400'
      } cursor-pointer transition-colors hover:text-gray-600`}
    >
      {icon}
    </span>
  );
};

export default function Supportlog({ driverDetails }: DocumentTabProps) {
  const driverId = driverDetails?.id;
  const [sortConfig, setSortConfig] = useState<{
    key: keyof SupportLog;
    direction: 'asc' | 'desc';
  } | null>(null);

  const {
    data: supportLogs = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<SupportLog[], Error>({
    queryKey: ['driverSupportLogs', driverId],
    queryFn: () => fetchDriverSupportLogs(driverId.toString()),
    enabled: !!driverId, // only fetch if driverId exists
  });

  const handleSort = (key: keyof SupportLog) => {
    setSortConfig(prevConfig =>
      prevConfig?.key === key
        ? { key, direction: prevConfig.direction === 'asc' ? 'desc' : 'asc' }
        : { key, direction: 'asc' }
    );
  };

  const sortedLogs = useMemo(() => {
    if (!sortConfig) return supportLogs;

    const sorted = [...supportLogs].sort((a, b) => {
      const valA = a[sortConfig.key];
      const valB = b[sortConfig.key];

      if (valA == null) return 1;
      if (valB == null) return -1;

      if (['created_at', 'resolution_date'].includes(sortConfig.key)) {
        return sortConfig.direction === 'asc'
          ? new Date(valA).getTime() - new Date(valB).getTime()
          : new Date(valB).getTime() - new Date(valA).getTime();
      }

      // If sorting by string
      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortConfig.direction === 'asc'
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }

      // Fallback
      return sortConfig.direction === 'asc'
        ? (valA as any) > (valB as any)
          ? 1
          : -1
        : (valA as any) < (valB as any)
          ? 1
          : -1;
    });

    return sorted;
  }, [supportLogs, sortConfig]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="border-l-4 border-red-500 bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">{error.message}</p>
          </div>
        </div>
      </div>
    );
  }

  if (supportLogs.length === 0) {
    return (
      <div className="border-l-4 border-blue-500 bg-blue-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              No support logs found for this driver.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tabled">
      <div className="overflow-hidden rounded-b-[12px] border-t-0 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="header-bar bg-tables flex items-center justify-between p-3 dark:bg-gray-800">
          {/* Search Bar */}
          <form className="max-w-md flex-1">
            <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Search
            </label>
            {/* input can go here */}
          </form>

          {/* Buttons Container */}
          <div className="flex items-center gap-2">
            {/* Filter Button */}
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                />
              </svg>
              Filters
            </button>

            {/* Refresh Button */}
            <button
              type="button"
              aria-label="refresh"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <PiClockCounterClockwiseLight size={22} />
            </button>

            {/* Download Button */}
            <button
              type="button"
              aria-label="download"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="size-4"
              >
                <path
                  fillRule="evenodd"
                  d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                  clip-rule="evenodd"
                />
              </svg>
              {/* <FaDownload size={20} /> */}
            </button>
          </div>
        </div>
        <div className="custom-scrollbar max-w-full overflow-x-auto">
          <div className="max-w-[900px] min-w-[-webkit-fill-available]">
            <Table>
              <TableHeader className="bg-tables dark:border-white/[0.05 border-b">
                <TableRow className="border-t">
                  <TableCell isHeader className="w-4 px-4 py-3">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 rounded text-blue-600"
                    />
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('created_at')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('created_at');
                      }}
                    >
                      Date/Time
                      <SortIcon
                        direction={
                          sortConfig?.key === 'created_at'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'created_at'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('issue_id')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('issue_id');
                      }}
                    >
                      Issue ID
                      <SortIcon
                        direction={
                          sortConfig?.key === 'issue_id'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'issue_id'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('issue_type')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('issue_type');
                      }}
                    >
                      Issue Type
                      <SortIcon
                        direction={
                          sortConfig?.key === 'issue_type'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'issue_type'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500"
                  >
                    Issue Description
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('priority')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('priority');
                      }}
                    >
                      Priority
                      <SortIcon
                        direction={
                          sortConfig?.key === 'priority'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'priority'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('status')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('status');
                      }}
                    >
                      Status
                      <SortIcon
                        direction={
                          sortConfig?.key === 'status'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'status'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="cursor-pointer px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('resolution_date')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('resolution_date');
                      }}
                    >
                      Resolution Date
                      <SortIcon
                        direction={
                          sortConfig?.key === 'resolution_date'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'resolution_date'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="cursor-pointer px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 select-none"
                  >
                    <div
                      onClick={() => handleSort('assigned_admin')}
                      className="flex cursor-pointer items-center"
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('assigned_admin');
                      }}
                    >
                      Assigned Admin
                      <SortIcon
                        direction={
                          sortConfig?.key === 'assigned_admin'
                            ? sortConfig.direction
                            : 'asc'
                        }
                        active={sortConfig?.key === 'assigned_admin'}
                      />
                    </div>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500"
                  />
                </TableRow>
              </TableHeader>

              <TableBody className="divide-y dark:divide-white/[0.05]">
                {sortedLogs.map(log => (
                  <TableRow
                    key={log.id}
                    className="bg-tables cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <TableCell className="w-4 px-4 py-3">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 rounded text-blue-600"
                      />
                    </TableCell>

                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-[12px] text-[#050013]">
                        {formatDateTime(log.created_at)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-[12px] font-medium text-[#050013]">
                        {log.issue_id}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-[12px] text-[#050013]">
                        {log.issue_type}
                      </p>
                    </TableCell>
                    <TableCell className="max-w-[210px] px-4 py-3 whitespace-nowrap">
                      <p className="line-clamp-2 text-[12px] text-[#050013]">
                        {log.issue_description}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <Badge size="sm" color={getPriorityColor(log.priority)}>
                        {log.priority}
                      </Badge>
                    </TableCell>
                    {/* <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <div className="flex items-center gap-1">
                                                <span className={`inline-block w-2 h-2 rounded-full ${log.status === 'resolved' ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                                                <span className="text-[#050013] text-[12px] capitalize">{log.status}</span>
                                            </div>
                                        </TableCell> */}
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center gap-1">
                        <span
                          className={`inline-block h-2 w-2 rounded-full ${
                            log.status === 'resolved'
                              ? 'bg-green-500'
                              : 'bg-yellow-500'
                          }`}
                        ></span>
                        <span
                          className={`text-[12px] capitalize ${
                            log.status === 'resolved'
                              ? 'text-green-600'
                              : 'text-yellow-600'
                          }`}
                        >
                          {log.status}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-[12px] text-[#050013]">
                        {log.resolution_date
                          ? formatDateTime(log.resolution_date)
                          : 'N/A'}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-[12px] text-[#050013]">
                        {log.assigned_admin}
                      </p>
                    </TableCell>

                    <TableCell className="text-theme-sm px-4 text-gray-500 dark:text-gray-400">
                      <Tippy
                        content={
                          <div className="bg-white text-gray-900">
                            <div className="flex flex-col space-y-1 p-1">
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                View Details
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                {log.status === 'resolved'
                                  ? 'Reopen Issue'
                                  : 'Resolve Issue'}
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Add Comment
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Download Log
                              </button>
                            </div>
                          </div>
                        }
                        interactive={true}
                        placement="left"
                        theme="light"
                        arrow={false}
                        duration={0}
                        className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                      >
                        <button
                          type="button"
                          className="focus:outline-none"
                          aria-label="actions"
                        >
                          <BsThreeDotsVertical />
                        </button>
                      </Tippy>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}
