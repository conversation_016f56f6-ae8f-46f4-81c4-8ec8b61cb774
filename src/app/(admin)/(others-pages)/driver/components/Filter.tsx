import { Menu, MenuItem, Transition, MenuItems } from '@headlessui/react';
import { Fragment, useState } from 'react';
import Select from '../../../../../components/form/Select';
import { ChevronDownIcon } from '@/icons';
import Input from '../../../../../components/form/input/InputField';

type FilterProps = {
  filteredData: any;
  setFilters: React.Dispatch<
    React.SetStateAction<{
      name: string;
      shift: string;
      ratings: string;
      region: string;
      rides: string;
    }>
  >;
  filtersDataFuc: () => void;
};

export default function FilterComponent(props: FilterProps) {
  const { filteredData, setFilters, filtersDataFuc } = props;

  // Local state to hold filter values before applying
  const [localFilters, setLocalFilters] = useState({
    name: '',
    shift: '',
    ratings: '',
    region: '',
    rides: '',
  });

  const handleApplyFilters = () => {
    // Update the parent component's filters with local filter values

    console.log(localFilters);

    setFilters({
      name: localFilters.name,
      shift: localFilters.shift,
      ratings: localFilters.ratings,
      region: localFilters.region,
      rides: localFilters.rides,
    });
    // Call the filter function
    filtersDataFuc();
  };

  const handleSelectChange =
    (field: keyof typeof localFilters) => (selectedOption: any) => {
      // If selectedOption is the direct value (not an object):
      const value =
        typeof selectedOption === 'object'
          ? selectedOption?.value
          : selectedOption;

      setLocalFilters(prev => ({
        ...prev,
        [field]: value || '',
      }));
    };

  return (
    <Menu as="div" className="relative inline-block text-left">
      <Menu.Button className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
          className="h-4 w-4"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
          />
        </svg>
        Filters
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <MenuItems className="absolute right-0 mt-2 w-80 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
          <div className="flex flex-col gap-4 px-4 py-4">
            <h4>Filters</h4>

            <MenuItem onClick={event => event.preventDefault()}>
              {() => (
                <Input
                  type="text"
                  placeholder="Full Name"
                  className="mb-3"
                  onChange={e =>
                    setLocalFilters(prev => ({ ...prev, name: e.target.value }))
                  }
                  name="Driver Name"
                  value={localFilters.name}
                />
              )}
            </MenuItem>

            <MenuItem onClick={event => event.preventDefault()}>
              {() => (
                <div>
                  <div className="relative">
                    <Select
                      options={[
                        { value: 'morning', label: 'Morning' },
                        { value: 'night', label: 'Night' },
                      ]}
                      placeholder="Shift"
                      onChange={handleSelectChange('shift')}
                      className="dark:bg-dark-900 w-100"
                      value={
                        localFilters.shift
                          ? {
                              value: localFilters.shift,
                              label:
                                localFilters.shift === 'morning'
                                  ? 'Morning'
                                  : 'Night',
                            }
                          : null
                      }
                    />
                    <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                </div>
              )}
            </MenuItem>

            <MenuItem onClick={event => event.preventDefault()}>
              {() => (
                <div>
                  <div className="relative">
                    <Select
                      options={[
                        { value: '1', label: '1 ★' },
                        { value: '1.5', label: '1.5 ★' },
                        { value: '2', label: '2 ★' },
                        { value: '2.5', label: '2.5 ★' },
                        { value: '3', label: '3 ★' },
                        { value: '3.5', label: '3.5 ★' },
                        { value: '4', label: '4 ★' },
                        { value: '4.5', label: '4.5 ★' },
                        { value: '5', label: '5 ★' },
                      ]}
                      placeholder="Ratings"
                      onChange={handleSelectChange('ratings')}
                      className="dark:bg-dark-900 w-100"
                      value={
                        localFilters.ratings
                          ? {
                              value: localFilters.ratings,
                              label: `${localFilters.ratings} ★`,
                            }
                          : null
                      }
                    />
                    <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                </div>
              )}
            </MenuItem>

            <MenuItem onClick={event => event.preventDefault()}>
              {() => (
                <div>
                  <div className="relative">
                    <Select
                      options={[
                        { value: '50', label: 'More than 50' },
                        { value: '100', label: 'More than 100' },
                        { value: '150', label: 'More than 150' },
                        { value: '200', label: 'More than 200' },
                        { value: '250', label: 'More than 250' },
                        { value: '300', label: 'More than 300' },
                        { value: '350', label: 'More than 350' },
                        { value: '400', label: 'More than 400' },
                      ]}
                      placeholder="Rides"
                      onChange={handleSelectChange('rides')}
                      className="dark:bg-dark-900 w-100"
                      value={
                        localFilters.rides
                          ? {
                              value: localFilters.rides,
                              label: `More than ${localFilters.rides}`,
                            }
                          : null
                      }
                    />
                    <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                      <ChevronDownIcon />
                    </span>
                  </div>
                </div>
              )}
            </MenuItem>

            <div className="flex items-end justify-end">
              <button
                className="items-right me-2 mb-2 flex w-fit gap-2 rounded-full bg-blue-700 px-6 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                onClick={handleApplyFilters}
              >
                Apply
              </button>
            </div>
          </div>
        </MenuItems>
      </Transition>
    </Menu>
  );
}
