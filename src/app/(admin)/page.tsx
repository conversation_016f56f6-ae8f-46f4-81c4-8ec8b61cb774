import type { Metadata } from 'next';
import { EcommerceMetrics } from '@/components/ecommerce/EcommerceMetrics';
import React from 'react';
import MonthlyTarget from '@/components/ecommerce/MonthlyTarget';
import MonthlySalesChart from '@/components/ecommerce/MonthlySalesChart';
import StatisticsChart from '@/components/ecommerce/StatisticsChart';
import RecentOrders from '@/components/ecommerce/RecentOrders';
import DemographicCard from '@/components/ecommerce/DemographicCard';

export const metadata: Metadata = {
  title: 'Taxificency',
  description: '',
};

export default function Ecommerce() {
  return (
    <div className="grid grid-cols-12 gap-4 p-2 md:gap-6">
      <div className="col-span-12 space-y-6 xl:col-span-7">
        <EcommerceMetrics />

        <MonthlySalesChart />
      </div>

      <div className="col-span-12 xl:col-span-5">
        <MonthlyTarget />
      </div>

      <div className="col-span-12">
        <StatisticsChart />
      </div>

      {/* <div className="col-span-12 xl:col-span-5">
        <DemographicCard />
      </div>

      <div className="col-span-12 xl:col-span-7">
        <RecentOrders />
      </div> */}
    </div>
  );
}
