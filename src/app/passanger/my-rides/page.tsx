'use client';

import React, { useEffect, useState } from 'react';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

// FULL ICON + COMPONENT IMPORTS (as requested)
import {
  PiCalendarDot,
  PiClockCounterClockwiseLight,
  PiCalendarDotThin,
} from 'react-icons/pi';
import { FiMic, FiCamera, FiX } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { TfiCheckBox } from 'react-icons/tfi';
import { IoIosArrowForward, IoIosSearch } from 'react-icons/io';
import {
  TbWheelchair,
  TbMap2,
  TbHelpTriangle,
  TbChevronDown,
  TbMoonStars,
  TbSunHigh,
  TbFilter,
  TbMapPin,
} from 'react-icons/tb';
import {
  LuPhone,
  LuMessageSquareMore,
  <PERSON><PERSON><PERSON>,
  LuFlagTriangleRight,
  <PERSON>Euro,
} from 'react-icons/lu';
import {
  FaPhone,
  FaCommentDots,
  FaUser,
  FaWheelchair,
  FaLocationArrow,
  FaPlus,
} from 'react-icons/fa';
import { GrLocation } from 'react-icons/gr';
import { CiClock2 } from 'react-icons/ci';
import { SlArrowLeft } from 'react-icons/sl';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Link from 'next/link';

// Sample Data
const upcomingRides = [
  {
    id: '#RD45692',
    from: 'Spuistraat City',
    to: 'Schipol Airport',
    date: 'Feb 15, 2025',
    time: '9:30 AM',
    price: '€32.50',
    type: 'today',
  },
  {
    id: '#RD45693',
    from: 'City Center',
    to: 'Hotel Airport',
    date: 'Feb 28, 2025',
    time: '12:30 PM',
    price: '€32.50',
    type: 'later',
  },
  {
    id: '#RD45694',
    from: 'City Center',
    to: 'Hotel Airport',
    date: 'Mar 1, 2025',
    time: '12:30 PM',
    price: '€32.50',
    type: 'later',
  },
];

const completedRides = [
  {
    id: '#RD45500',
    from: 'Hotel Airport',
    to: 'Downtown',
    date: 'Jan 10, 2025',
    time: '2:00 PM',
    price: '€28.00',
  },
];

// Ride Card Component
const RideCard = ({ id, from, to, date, time, price }) => (
  <div className="flex items-start justify-between rounded-xl bg-white px-6 py-5 shadow-md">
    <div className="space-y-1">
      <p className="text-sm font-medium text-[#5B5B5B]">{id}</p>
      <div className="flex items-center gap-2 text-sm font-semibold text-black">
        <span>{from}</span>
        <span className="text-gray-400">•••••</span>
        <span className="text-primary-500">{to}</span>
      </div>
      <p className="text-sm text-gray-500">
        {date} • {time}
      </p>
      <p className="mt-1 text-lg font-semibold">{price}</p>
    </div>
    <button className="rounded-full border border-gray-400 px-4 py-1 text-sm text-gray-700 hover:bg-gray-100">
      Cancel
    </button>
  </div>
);

// Main Page
export default function MyRidesPage() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    if (setTitle) setTitle('Passenger Help');
  }, [setTitle]);

  const todayRides = upcomingRides.filter(r => r.type === 'today');
  const laterRides = upcomingRides.filter(r => r.type === 'later');
  const [showModal, setShowModal] = useState(false);
  const [isCanceled, setIsCanceled] = useState(false);
  return (
    <div className="mx-auto p-6">
      <TabGroup>
        <TabList className="flex gap-6 bg-white px-6 py-4">
          <Tab
            className={({ selected }) =>
              `flex items-center space-x-2 border-b-2 pb-3 text-sm font-medium outline-none ${selected ? 'text-purple border-blue-600' : 'border-transparent text-[#76787A]'}`
            }
          >
            {' '}
            <PiCalendarDot className="h-[20px] w-[20px]" />{' '}
            <span>Upcoming</span>{' '}
          </Tab>
          <Tab
            className={({ selected }) =>
              `flex items-center space-x-2 border-b-2 pb-3 text-sm font-medium outline-none ${selected ? 'text-purple border-blue-600' : 'border-transparent text-[#76787A]'}`
            }
          >
            {' '}
            <TfiCheckBox className="h-[20px] w-[20px]" />{' '}
            <span>Completed</span>{' '}
          </Tab>
        </TabList>

        <TabPanels className="mt-6 space-y-6">
          {/* Upcoming Tab Panel */}
          <TabPanel className="space-y-6">
            {/* Today Section */}
            {todayRides.length > 0 && (
              <div className="space-y-3">
                <p className="text-[14px] font-normal text-[#76787A]">Today</p>
                {todayRides.map((ride, idx) => (
                  <div
                    key={idx}
                    className={`mb-6 flex flex-col items-center justify-between gap-4 rounded-xl bg-white p-6 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row ${
                      isCanceled ? 'opacity-70' : ''
                    }`}
                  >
                    {/* Left: Ride details */}
                    <div className="w-full space-y-1">
                      <p className="text-purple text-[16px] font-medium">
                        {ride.id}
                      </p>

                      <div className="flex items-center gap-2 truncate text-[16px] font-medium text-[#050013]">
                        <span>{ride.from}</span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="w-10 border-t border-dashed border-gray-400"></span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="truncate">{ride.to}</span>
                      </div>

                      <p className="text-[14px] font-normal text-[#76787A]">
                        {ride.date} • {ride.time}
                      </p>

                      {/* Mobile View: Price + Cancel inline */}
                      <div className="flex items-center justify-between pt-1 sm:hidden">
                        <p className="text-[18px] font-semibold">
                          {ride.price}
                        </p>
                        <button
                          onClick={() => {
                            if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
                          }}
                          className={`rounded-full px-4 py-1 text-sm transition ${
                            isCanceled
                              ? 'cursor-default border border-red-400 bg-red-100 text-red-600'
                              : 'border border-gray-400 text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {isCanceled ? 'Canceled' : 'Cancel'}
                        </button>
                      </div>

                      {/* Desktop-only price */}
                      <p className="hidden text-[20px] font-semibold text-[#050013] sm:block">
                        {ride.price}
                      </p>
                    </div>

                    {/* Desktop-only Cancel button */}
                    <div className="hidden items-start sm:flex">
                      <button
                        onClick={() => {
                          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
                        }}
                        className={`rounded-full px-4 py-1 text-sm transition ${
                          isCanceled
                            ? 'cursor-default border border-transparent text-red-600'
                            : 'btn-border border text-[#76787A] hover:bg-gray-100'
                        }`}
                      >
                        {isCanceled ? 'Canceled' : 'Cancel'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {todayRides.length > 0 && (
              <div className="space-y-3">
                {todayRides.map((ride, idx) => (
                  <div
                    key={idx}
                    className={`mb-6 flex flex-col items-center justify-between gap-4 rounded-xl bg-white p-6 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row ${
                      isCanceled ? 'opacity-50' : ''
                    }`}
                  >
                    {/* Left: Ride details */}
                    <div className="w-full space-y-1">
                      <p className="text-purple text-[16px] font-medium">
                        {ride.id}
                      </p>

                      <div className="flex items-center gap-2 truncate text-[16px] font-medium text-[#050013]">
                        <span>{ride.from}</span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="w-10 border-t border-dashed border-gray-400"></span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="truncate">{ride.to}</span>
                      </div>

                      <p className="text-[14px] font-normal text-[#76787A]">
                        {ride.date} • {ride.time}
                      </p>

                      {/* Mobile View: Price + Cancel inline */}
                      <div className="flex items-center justify-between pt-1 sm:hidden">
                        <p className="text-[18px] font-semibold">
                          {ride.price}
                        </p>
                        <button
                          onClick={() => {
                            if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
                          }}
                          className={`rounded-full px-4 py-1 text-sm transition ${
                            isCanceled
                              ? 'cursor-default border border-transparent text-red-600'
                              : 'btn-border border text-[#76787A] hover:bg-gray-100'
                          }`}
                        >
                          {isCanceled ? 'Canceled' : 'Cancel'}
                        </button>
                      </div>

                      {/* Desktop-only price */}
                      <p className="hidden text-[20px] font-semibold text-[#050013] sm:block">
                        {ride.price}
                      </p>
                    </div>

                    {/* Desktop-only Cancel button */}
                    <div className="hidden items-start sm:flex">
                      <button
                        onClick={() => {
                          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
                        }}
                        className={`rounded-full px-4 py-1 text-sm transition ${
                          isCanceled
                            ? 'cursor-default border border-transparent text-red-600'
                            : 'btn-border border text-[#76787A] hover:bg-gray-100'
                        }`}
                      >
                        {isCanceled ? 'Canceled' : 'Cancel'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Later Section */}
            {laterRides.length > 0 && (
              <div className="space-y-3">
                <p className="text-[14px] font-normal text-[#76787A]">Later</p>

                {laterRides.map((ride, idx) => (
                  <div
                    key={idx}
                    className="mb-6 flex flex-col items-center justify-between gap-4 rounded-xl bg-white p-6 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row"
                  >
                    {/* Left: Ride details */}
                    <div className="w-full space-y-1">
                      <p className="text-purple text-[16px] font-medium">
                        {ride.id}
                      </p>

                      <div className="flex items-center gap-2 truncate text-[16px] font-medium text-[#050013]">
                        <span>{ride.from}</span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="w-10 border-t border-dashed border-gray-400"></span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="truncate">{ride.to}</span>
                      </div>

                      <p className="text-[14px] font-normal text-[#76787A]">
                        {ride.date} • {ride.time}
                      </p>

                      {/* Responsive row for Price + Cancel */}
                      <div className="flex items-center justify-between pt-1 sm:hidden">
                        <p className="text-[18px] font-semibold">
                          {ride.price}
                        </p>
                        <button
                          onClick={() => {
                            if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
                          }}
                          className={`rounded-full px-4 py-1 text-sm transition ${
                            isCanceled
                              ? 'cursor-default border border-transparent text-red-600'
                              : 'btn-border border text-[#76787A] hover:bg-gray-100'
                          }`}
                        >
                          {isCanceled ? 'Canceled' : 'Cancel'}
                        </button>
                      </div>

                      {/* Desktop-only price */}
                      <p className="hidden text-[20px] font-semibold text-[#050013] sm:block">
                        {ride.price}
                      </p>
                    </div>

                    {/* Desktop-only cancel button */}
                    <div className="hidden items-start sm:flex">
                      <button
                        onClick={() => {
                          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
                        }}
                        className={`rounded-full px-4 py-1 text-sm transition ${
                          isCanceled
                            ? 'cursor-default border border-transparent text-red-600'
                            : 'btn-border border text-[#76787A] hover:bg-gray-100'
                        }`}
                      >
                        {isCanceled ? 'Canceled' : 'Cancel'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabPanel>

          {/* Completed Tab Panel */}
          <TabPanel className="space-y-6">
            {todayRides.length > 0 && (
              <div className="space-y-3">
                <p className="text-[14px] font-normal text-[#76787A]">Today</p>

                {todayRides.map((ride, idx) => (
                  <div
                    key={idx}
                    className={`mb-6 flex flex-col items-center justify-between gap-4 rounded-xl bg-white p-6 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row ${
                      isCanceled ? 'opacity-70' : ''
                    }`}
                  >
                    {/* Left: Ride details */}
                    <div className="space-y-1">
                      <p className="text-purple text-[16px] font-medium">
                        {ride.id}
                      </p>

                      <div className="flex items-center gap-2 truncate text-[16px] font-medium text-[#050013]">
                        <span>{ride.from}</span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="w-10 border-t border-dashed border-gray-400"></span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="truncate">{ride.to}</span>
                      </div>

                      <p className="text-[14px] font-normal text-[#76787A]">
                        {ride.date} • {ride.time}
                      </p>

                      {/* Mobile View: Price + Re-Book inline */}
                      <div className="flex items-center justify-between pt-1 sm:hidden">
                        <p className="text-[18px] font-semibold">
                          {ride.price}
                        </p>
                        <button className="rounded-full border px-4 py-1.5 text-[14px] text-[#76787A] hover:bg-gray-100">
                          Re-Book
                        </button>
                      </div>

                      {/* Desktop-only price */}
                      <p className="hidden text-[20px] font-semibold text-[#050013] sm:block">
                        {ride.price}
                      </p>
                    </div>

                    {/* Desktop-only Re-Book button */}
                    <div className="hidden items-start sm:flex">
                      <button className="rounded-full border px-4 py-2 text-[14px] text-[#76787A] hover:bg-gray-100">
                        Re-Book
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {todayRides.length > 0 && (
              <div className="space-y-3">
                {todayRides.map((ride, idx) => (
                  <div
                    key={idx}
                    className={`mb-6 flex flex-col items-center justify-between gap-4 rounded-xl bg-white p-6 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row ${
                      isCanceled ? 'opacity-70' : ''
                    }`}
                  >
                    {/* Left: Ride details */}
                    <div className="space-y-1">
                      <p className="text-purple text-[16px] font-medium">
                        {ride.id}
                      </p>

                      <div className="flex items-center gap-2 truncate text-[16px] font-medium text-[#050013]">
                        <span>{ride.from}</span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="w-10 border-t border-dashed border-gray-400"></span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="truncate">{ride.to}</span>
                      </div>

                      <p className="text-[14px] font-normal text-[#76787A]">
                        {ride.date} • {ride.time}
                      </p>

                      {/* Mobile View: Price + Re-Book inline */}
                      <div className="flex items-center justify-between pt-1 sm:hidden">
                        <p className="text-[18px] font-semibold">
                          {ride.price}
                        </p>
                        <button className="rounded-full border px-4 py-1.5 text-[14px] text-[#76787A] hover:bg-gray-100">
                          Re-Book
                        </button>
                      </div>

                      {/* Desktop-only price */}
                      <p className="hidden text-[20px] font-semibold text-[#050013] sm:block">
                        {ride.price}
                      </p>
                    </div>

                    {/* Desktop-only Re-Book button */}
                    <div className="hidden items-start sm:flex">
                      <button className="rounded-full border px-4 py-2 text-[14px] text-[#76787A] hover:bg-gray-100">
                        Re-Book
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {todayRides.length > 0 && (
              <div className="space-y-3">
                {todayRides.map((ride, idx) => (
                  <div
                    key={idx}
                    className={`mb-6 flex flex-col items-center justify-between gap-4 rounded-xl bg-white p-6 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row ${
                      isCanceled ? 'opacity-70' : ''
                    }`}
                  >
                    {/* Left: Ride details */}
                    <div className="space-y-1">
                      <p className="text-purple text-[16px] font-medium">
                        {ride.id}
                      </p>

                      <div className="flex items-center gap-2 truncate text-[16px] font-medium text-[#050013]">
                        <span>{ride.from}</span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="w-10 border-t border-dashed border-gray-400"></span>
                        <span className="bg-cstm-blue-700 rounded-full p-1"></span>
                        <span className="truncate">{ride.to}</span>
                      </div>

                      <p className="text-[14px] font-normal text-[#76787A]">
                        {ride.date} • {ride.time}
                      </p>

                      {/* Mobile View: Price + Re-Book inline */}
                      <div className="flex items-center justify-between pt-1 sm:hidden">
                        <p className="text-[18px] font-semibold">
                          {ride.price}
                        </p>
                        <button className="rounded-full border px-4 py-1.5 text-[14px] text-[#76787A] hover:bg-gray-100">
                          Re-Book
                        </button>
                      </div>

                      {/* Desktop-only price */}
                      <p className="hidden text-[20px] font-semibold text-[#050013] sm:block">
                        {ride.price}
                      </p>
                    </div>

                    {/* Desktop-only Re-Book button */}
                    <div className="hidden items-start sm:flex">
                      <button className="rounded-full border px-4 py-2 text-[14px] text-[#76787A] hover:bg-gray-100">
                        Re-Book
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabPanel>
        </TabPanels>
      </TabGroup>

      {showModal && (
        <div className="fixed inset-0 z-999 flex items-center justify-center bg-[#222222]/70 px-4">
          <div className="w-full max-w-sm rounded-2xl bg-white p-6 text-center shadow-lg">
            {/* Title */}
            <h2 className="mb-2 text-[18px] font-medium text-[#050013]">
              Cancel Ride
            </h2>

            {/* Description */}
            <p className="mb-6 text-[13px] text-[#76787A]">
              Are you sure you want to cancel this ride? <br />A cancellation
              fee may apply based on the ride policy.
            </p>

            {/* Buttons */}
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setShowModal(false)}
                className="rounded-full border border-[#4A3AFF] px-6 py-2 font-medium text-[#4A3AFF] transition hover:bg-indigo-50"
              >
                No
              </button>
              <button
                onClick={() => {
                  setIsCanceled(true); // ✅ Change button label
                  setShowModal(false); // ✅ Close modal
                }}
                className="rounded-full bg-[#4A3AFF] px-6 py-2 font-medium text-white transition hover:bg-[#382fde]"
              >
                Yes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
