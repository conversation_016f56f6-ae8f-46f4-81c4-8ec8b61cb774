'use client';

import { useState, useEffect } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { FiPhone, FiX, FiCamera, FiMic } from 'react-icons/fi';
import { RxChevronLeft } from 'react-icons/rx';
import { FaCircle, FaPaperPlane } from 'react-icons/fa';
import { LuClock3 } from 'react-icons/lu';

export default function ChatPage() {
  const { setTitle } = useHeaderTitle();
  const [message, setMessage] = useState('');
  const [selectedQuick, setSelectedQuick] = useState(null);
  const [popupOpened, setPopupOpened] = useState<boolean>(false);

  useEffect(() => {
    setTitle('Chat');
  }, [setTitle]);

  const messages = [
    {
      type: 'received',
      text: 'Hi Rivka! Where are you parked',
      time: '9:15 AM',
      avatar: '/user1.png',
    },
    {
      type: 'sent',
      text: "Hi <PERSON>! I'm on my way to pick you up at Spuistraat City Centre, ETA: 5 minutes.",
      time: '9:16 AM',
      avatar: '/user2.png',
    },
    {
      type: 'info',
      text: 'Waiting time: 5 min',
    },
  ];

  const quickReplies = [
    "I'll be there in 2 mins",
    'Traffic delay',
    'I have arrived',
    "Can't find you",
  ];

  const handleQuickClick = text => {
    setMessage(text);
    setSelectedQuick(text);
  };

  return (
    <div className="flex h-[calc(100vh-80px)] flex-col rounded-2xl border bg-white">
      {/* Header */}
      <div className="flex items-center justify-between border-b p-6">
        <div className="flex items-start">
          <RxChevronLeft className="m-[5px] text-[#76787A]" size={20} />
          <div>
            <p className="text-[18px] font-semibold text-[#050013]">
              Rivka Frank
            </p>
            <div className="flex items-center gap-4">
              <p className="text-[13px] font-normal text-[#76787A]">Driver</p>
              <span className="flex items-center gap-1 text-[13px] text-green-600">
                <FaCircle size={10} />
                Online
              </span>
            </div>
          </div>
        </div>
        <span className="border-btn rounded-full p-3">
          <FiPhone className="text-purple text-xl" />
        </span>
      </div>

      {/* Chat body */}
      <div className="flex-1 space-y-4 overflow-y-auto p-6">
        <div className="flex justify-center">
          <span className="rounded-full bg-[#F4F5F7] px-5 py-2 text-xs text-[#050013]">
            Today
          </span>
        </div>

        {messages.map((msg, i) => (
          <div
            key={i}
            className={`flex ${msg.type === 'sent' ? 'justify-end' : 'justify-start'}`}
          >
            {msg.type === 'received' && (
              <div className="flex gap-2">
                <img
                  src="/images/avatar.jpg"
                  alt="avatar"
                  className="h-8 w-8 rounded-full"
                />
                <div>
                  <div className="max-w-[280px] rounded-full rounded-tl-none bg-[#F4F5F7] px-5 py-2 text-[13px] text-[#050013]">
                    {msg.text}
                  </div>
                  <p className="mt-1 text-[12px] text-[#76787A]">{msg.time}</p>
                </div>
              </div>
            )}

            {msg.type === 'sent' && (
              <div className="flex flex-col">
                <div className="flex items-start gap-2">
                  <div>
                    <div className="max-w-[350px] rounded-xl rounded-tr-none bg-gradient-to-r from-indigo-600 to-teal-400 px-5 py-2 text-[13px] text-white">
                      {msg.text}
                    </div>
                    <p className="mt-1 text-left text-[12px] text-[#76787A]">
                      {msg.time}
                    </p>
                  </div>
                  <img
                    src="/images/user2.jpg"
                    alt="avatar"
                    className="h-8 w-8 rounded-full"
                  />
                </div>
                <div className="mt-4 flex w-fit items-center gap-2 rounded-full bg-[#FFF4E5] px-4 py-2 text-[12px] text-[#050013]">
                  <span>
                    <LuClock3 className="text-[#C3801B]" size={18} />
                  </span>
                  <span>Waiting time: 5 min</span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Replies */}
      <div className="flex flex-wrap gap-2 p-6">
        {quickReplies.map((q, i) => (
          <button
            key={i}
            onClick={() => handleQuickClick(q)}
            className={`rounded-full border px-4 py-1.5 text-sm hover:bg-gray-100 ${
              selectedQuick === q
                ? 'border-btn text-purple bg-blue-50'
                : 'border-gray-300'
            }`}
          >
            {q}
          </button>
        ))}
      </div>

      {/* Message Input */}
      <div className="px-6 pb-6">
        <div className="flex items-center rounded-full border px-4 py-2">
          <input
            type="text"
            placeholder="Type your message..."
            className="flex-1 text-[13px] focus:!border-0 focus:ring-0 focus:outline-none"
            value={message}
            onChange={e => setMessage(e.target.value)}
          />
          <button className="text-purple !hover:bg-table rounded-full p-2">
            <FaPaperPlane className="text-lg" />
          </button>
        </div>
      </div>

      <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out sm:hidden">
        <div className="r absolute bottom-0 flex h-[600px] w-full translate-x-0 transform flex-col justify-between overflow-y-scroll rounded-tl-[18px] rounded-tr-[18px] bg-white shadow-lg transition-all duration-500 sm:relative sm:h-full sm:max-w-md sm:translate-x-0 sm:overflow-visible sm:rounded-tr-none">
          <div className="flex items-center justify-between border-b p-6">
            <div className="flex items-start">
              <RxChevronLeft className="m-[5px] text-[#76787A]" size={20} />
              <div>
                <p className="text-[18px] font-semibold text-[#050013]">
                  Rivka Frank
                </p>
                <div className="flex items-center gap-4">
                  <p className="text-[13px] font-normal text-[#76787A]">
                    Driver
                  </p>
                  <span className="flex items-center gap-1 text-[13px] text-green-600">
                    <FaCircle size={10} />
                    Online
                  </span>
                </div>
              </div>
            </div>
            <span className="border-btn rounded-full p-2">
              <FiPhone size={15} className="text-purple text-xl" />
            </span>
          </div>

          {/* Chat body */}
          <div className="flex-1 space-y-4 overflow-y-auto p-6">
            <div className="flex justify-center">
              <span className="rounded-full bg-[#F4F5F7] px-5 py-2 text-xs text-[#050013]">
                Today
              </span>
            </div>

            {messages.map((msg, i) => (
              <div
                key={i}
                className={`flex ${msg.type === 'sent' ? 'justify-end' : 'justify-start'}`}
              >
                {msg.type === 'received' && (
                  <div className="flex gap-2">
                    <img
                      src="/images/avatar.jpg"
                      alt="avatar"
                      className="h-8 w-8 rounded-full"
                    />
                    <div>
                      <div className="max-w-[280px] rounded-full rounded-tl-none bg-[#F4F5F7] px-5 py-2 text-[13px] text-[#050013]">
                        {msg.text}
                      </div>
                      <p className="mt-1 text-[12px] text-[#76787A]">
                        {msg.time}
                      </p>
                    </div>
                  </div>
                )}

                {msg.type === 'sent' && (
                  <div className="flex flex-col">
                    <div className="flex items-start gap-2">
                      <div>
                        <div className="max-w-[350px] rounded-xl rounded-tr-none bg-gradient-to-r from-indigo-600 to-teal-400 px-5 py-2 text-[13px] text-white">
                          {msg.text}
                        </div>
                        <p className="mt-1 text-left text-[12px] text-[#76787A]">
                          {msg.time}
                        </p>
                      </div>
                      <img
                        src="/images/user2.jpg"
                        alt="avatar"
                        className="h-8 w-8 rounded-full"
                      />
                    </div>
                    <div className="mt-4 flex w-fit items-center gap-2 rounded-full bg-[#FFF4E5] px-4 py-2 text-[12px] text-[#050013]">
                      <span>
                        <LuClock3 className="text-[#C3801B]" size={18} />
                      </span>
                      <span>Waiting time: 5 min</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Quick Replies */}
          <div className="flex flex-wrap gap-2 p-6">
            {quickReplies.map((q, i) => (
              <button
                key={i}
                onClick={() => handleQuickClick(q)}
                className={`rounded-full border px-4 py-1.5 text-sm hover:bg-gray-100 ${
                  selectedQuick === q
                    ? 'border-btn text-purple bg-blue-50'
                    : 'border-gray-300'
                }`}
              >
                {q}
              </button>
            ))}
          </div>

          {/* Message Input */}
          <div className="px-6 pb-6">
            <div className="flex items-center rounded-full border px-4 py-2">
              <input
                type="text"
                placeholder="Type your message..."
                className="flex-1 text-[13px] focus:!border-0 focus:ring-0 focus:outline-none"
                value={message}
                onChange={e => setMessage(e.target.value)}
              />
              <button
                className="text-purple !hover:bg-table rounded-full p-2"
                onClick={() => setPopupOpened(true)}
              >
                <FaPaperPlane className="text-lg" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
