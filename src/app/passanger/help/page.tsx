'use client';

import { useEffect, useState } from 'react';
import {
  FiPhone,
  FiMessageSquare,
  FiChevronRight,
  FiPlus,
  FiMinus,
} from 'react-icons/fi';
import { LiaCarSideSolid, LiaInboxSolid } from 'react-icons/lia';
import { MdOutlinePayment, MdOutlineMail } from 'react-icons/md';
import { HiOutlineCurrencyEuro } from 'react-icons/hi2';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { IoIosSearch } from 'react-icons/io';
import { LuMessageSquareMore } from 'react-icons/lu';

export default function HelpPage() {
  const { setTitle } = useHeaderTitle();
  const [modalContent, setModalContent] = useState(null);
  const [faqOpenIndex, setFaqOpenIndex] = useState(null);

  useEffect(() => {
    if (setTitle) setTitle('Passenger Help');
  }, [setTitle]);

  const openModal = content => setModalContent(content);
  const closeModal = () => setModalContent(null);

  const faqItems = [
    'How do I request a refund',
    'Where can I see my ride history',
    'How to contact my driver',
  ];

  return (
    <div className="relative">
      {/* Main Content */}
      <div className="space-y-6 p-4 sm:p-6">
        <div className="bg-tables rounded-xl border p-6">
          {/* Search */}
          <div className="relative mb-6 w-full">
            <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4 text-gray-400">
              <IoIosSearch size={22} />
            </span>
            <input
              type="text"
              placeholder="Search for help"
              className="border-gray w-full rounded-xl border py-3 pr-4 pl-11 text-[14px] focus:ring-2 focus:ring-indigo-200 focus:outline-none"
            />
          </div>
          {/* 24/7 Support */}
          <div className="grid grid-cols-2 gap-4">
            <div
              onClick={() => openModal('Live Chat')}
              className="flex cursor-pointer flex-col items-center gap-3 rounded-xl bg-white p-4 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row"
            >
              <div className="rounded-full bg-purple-100 p-3 text-purple-600">
                <LuMessageSquareMore className="text-sm" />
              </div>
              <p className="text-[13px] font-medium text-[#050013]">
                24/7 Live Chat
              </p>
            </div>
            <div
              onClick={() => openModal('Call Support')}
              className="flex cursor-pointer flex-col items-center gap-3 rounded-xl bg-white p-4 shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row"
            >
              <div className="rounded-full bg-purple-100 p-3 text-purple-600">
                <FiPhone className="text-sm" />
              </div>
              <p className="text-[13px] font-medium text-[#050013]">
                24/7 Call
              </p>
            </div>
          </div>
          {/* Common Issues */}
          <div>
            <h3 className="mt-6 mb-3 text-[13px] text-[#76787A]">
              Common issues
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div
                onClick={() => openModal('Ride Issue')}
                className="flex cursor-pointer items-center justify-between rounded-xl bg-white p-4 shadow-[0px_2px_15px_0px_#1D24610D]"
              >
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-red-100 p-3 text-red-500">
                    <LiaCarSideSolid className="text-xl" />
                  </div>
                  <p className="text-[13px] font-medium text-[#050013]">
                    Ride Issue
                  </p>
                </div>
                <FiChevronRight className="text-gray-400" />
              </div>
              <div
                onClick={() => openModal('Payment Issue')}
                className="flex cursor-pointer items-center justify-between rounded-xl bg-white p-4 shadow-[0px_2px_15px_0px_#1D24610D]"
              >
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-green-100 p-3 text-green-500">
                    <HiOutlineCurrencyEuro className="text-xl" />
                  </div>
                  <p className="text-[13px] font-medium text-[#050013]">
                    Payment issue
                  </p>
                </div>
                <FiChevronRight className="text-gray-400" />
              </div>
              <div
                onClick={() => openModal('Lost Items')}
                className="flex cursor-pointer items-center justify-between rounded-xl bg-white p-4 shadow-[0px_2px_15px_0px_#1D24610D]"
              >
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-gray-100 p-3 text-gray-500">
                    <LiaInboxSolid className="text-xl" />
                  </div>
                  <p className="text-[13px] font-medium text-[#050013]">
                    Lost Items
                  </p>
                </div>
                <FiChevronRight className="text-gray-400" />
              </div>
            </div>
          </div>
        </div>
        {/* FAQ */}
        <div className="rounded-xl border bg-white">
          <h3 className="bg-tables rounded-t-xl px-5 py-4 text-[14px] font-medium text-[#050013]">
            FAQ
          </h3>
          <div className="divide-y divide-gray-200 px-5">
            {faqItems.map((item, index) => (
              <div key={index} className="py-4">
                <div
                  className="flex cursor-pointer items-center justify-between"
                  onClick={() =>
                    setFaqOpenIndex(faqOpenIndex === index ? null : index)
                  }
                >
                  <p className="text-[14px] font-normal text-[#050013]">
                    {item}
                  </p>
                  {faqOpenIndex === index ? (
                    <FiMinus size={20} className="text-[#050013]" />
                  ) : (
                    <FiPlus size={20} className="text-purple" />
                  )}
                </div>
                {faqOpenIndex === index && (
                  <div className="mt-2 py-5 text-[14px] font-normal text-[#050013]">
                    This is the answer for {item}.
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Slide-in Modal Box (bottom right, fixed width) */}
      <div
        className={`fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out ${
          modalContent ? 'translate-x-0' : 'translate-x-full'
        } rounded-l-2xl`}
      >
        <div className="r absolute bottom-0 flex h-[600px] w-full translate-x-0 transform flex-col justify-between overflow-y-scroll rounded-tl-[18px] rounded-tr-[18px] bg-white p-6 shadow-lg transition-all duration-500 sm:relative sm:h-full sm:max-w-md sm:translate-x-0 sm:overflow-visible sm:rounded-tr-none">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold">{modalContent}</h2>
            <button
              onClick={closeModal}
              className="text-2xl text-gray-400 hover:text-gray-600"
            >
              &times;
            </button>
          </div>
          <div className="flex-1 text-sm text-gray-600">
            This is the content for <strong>{modalContent}</strong>. Add your
            details, chat, or help info here.
          </div>
        </div>
      </div>
    </div>
  );
}
