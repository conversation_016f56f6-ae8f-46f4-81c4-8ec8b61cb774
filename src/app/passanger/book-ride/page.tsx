'use client';

import React, { useEffect, useState } from 'react';
import { Phone, MapPin, Flag } from 'lucide-react';
import { IoMdStar } from 'react-icons/io';
import { LuFlagTriangleRight } from 'react-icons/lu';
import { FiChevronDown } from 'react-icons/fi';

export default function BookRidePage() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreen = () => {
      setIsMobile(window.innerWidth < 640);
    };
    checkScreen();
    window.addEventListener('resize', checkScreen);
    return () => window.removeEventListener('resize', checkScreen);
  }, []);

  return (
    <div className="flex min-h-screen flex-col gap-4 bg-white p-4 md:flex-row">
      {/* Left: Map */}
      <div className="h-[300px] w-full overflow-hidden rounded-3xl shadow md:h-[90vh] md:w-1/2">
        <iframe
          title="Map"
          className="h-full w-full"
          src="https://maps.google.com/maps?q=Nieuwe%20Amsterdamseweg&t=&z=15&ie=UTF8&iwloc=&output=embed"
          frameBorder="0"
          allowFullScreen
        />
      </div>

      {/* Right Card - visible only on desktop */}
      <div className="hidden h-fit max-h-[600px] w-full flex-col justify-between rounded-2xl border bg-white sm:flex md:w-1/2">
        <RideInfo />
      </div>

      {/* Mobile Modal - auto shown */}
      {isMobile && (
        <div className="fixed inset-0 z-999 flex justify-end bg-black/30 sm:hidden">
          <div className="h-[88%] w-full overflow-y-auto rounded-t-2xl bg-white shadow-lg sm:p-6">
            <RideInfo />
          </div>
        </div>
      )}
    </div>
  );
}

// Ride Info Component
const RideInfo = () => (
  <div className="w-full">
    <div className="h-[300px] w-full overflow-hidden shadow sm:hidden sm:rounded-3xl md:h-[90vh] md:w-1/2">
      <iframe
        title="Map"
        className="h-full w-full"
        src="https://maps.google.com/maps?q=Nieuwe%20Amsterdamseweg&t=&z=15&ie=UTF8&iwloc=&output=embed"
        frameBorder="0"
        allowFullScreen
      />
    </div>
    {/* Top Banner */}
    <div className="rounded-2xl rounded-b-none bg-[#050013] pt-4 pb-6 text-center text-sm font-medium text-white">
      Your Driver will Arrive on 15 Feb 2025, 10:30AM
    </div>
    <div className="-mt-2 rounded-2xl rounded-t-[12px] bg-white">
      {/* Driver Info */}
      <div className="mb-6 flex items-start gap-4 border-b p-6">
        <img
          src="/images/user.jpg"
          alt="Driver"
          className="h-12 w-12 rounded-full object-cover"
        />
        <div>
          <h3 className="flex items-center gap-2 text-[16px] font-medium text-[#050013]">
            Rivka Frank{' '}
            <span className="size={12} flex items-center gap-1 text-[#050013]">
              <IoMdStar size={18} className="text-[#FF9601]" />
              4.5
            </span>
          </h3>
          <p className="text-[13px] text-[#76787A]">Toyota Camry · Black</p>
          <p className="mt-1 text-[14px] font-semibold text-[#050013]">
            NL-00–12568
          </p>
        </div>
        <button className="border-btn ml-auto rounded-full p-3">
          <Phone size={18} className="text-purple" />
        </button>
      </div>

      {/* Pickup & Drop Info */}
      <div className="relative mb-6 space-y-6 px-6">
        {/* Line between icons */}
        <div className="absolute top-6 bottom-6 left-[7.3%] z-0 h-[40%] border-l border-dashed border-gray-300 sm:left-[6.3%]"></div>

        {/* Pick up */}
        <div className="relative z-10 flex items-start gap-3">
          <span className="z-10 rounded-full bg-blue-50 p-1">
            <MapPin className="text-purple" size={16} />
          </span>
          <div>
            <p className="text-[12px] text-[#76787A]">Pick up</p>
            <p className="text-[14px] font-normal text-[#050013]">
              288–292, Spuistraat City
            </p>
          </div>
        </div>

        {/* Drop off */}
        <div className="relative z-10 flex items-start gap-3">
          <span className="z-10 rounded-full bg-blue-50 p-1">
            <LuFlagTriangleRight className="text-purple" size={16} />
          </span>
          <div>
            <p className="text-[12px] text-[#76787A]">Drop off</p>
            <p className="text-[14px] font-normal text-[#050013]">
              1000 AP, Schipol Airport
            </p>
          </div>
        </div>
      </div>

      <div className="px-6 pb-6">
        {/* Fare Estimate */}
        <div className="bg-tables mb-6 flex items-center justify-between rounded-xl px-4 py-3">
          {/* Label + Icon + Dropdown */}
          <div className="mb-2 flex items-center justify-between">
            <div className="relative w-[100%]">
              {/* Right Chevron */}
              <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 transform rounded-full bg-white p-[2px] text-lg text-[#050013]">
                <FiChevronDown />
              </span>

              {/* Select Box */}
              <select
                id="fareType"
                className="appearance-none pr-10 text-[14px] text-[#76787A] focus:ring-2 focus:ring-[#3B00F3] focus:outline-none"
              >
                <option>Fare Estimate</option>
                <option>Premium</option>
                <option>Shared</option>
              </select>
            </div>
          </div>

          {/* Right Price */}
          <span className="text-[18px] font-medium">€ 35.00</span>
        </div>

        {/* Cancel Ride Button */}
        <button className="btn-border w-full rounded-full py-3 text-center text-[14px] font-semibold text-[#76787A] transition hover:bg-gray-100">
          Cancel Ride
        </button>
      </div>
    </div>
  </div>
);
