'use client';
import React, { useState } from 'react';
import {
  MapPin,
  Calendar,
  Clock,
  Car,
  Zap,
  Truck,
  VolumeX,
  Heart,
  CreditCard,
  Banknote,
  User,
} from 'lucide-react';
import { LuMapPin, LuFlagTriangleRight } from 'react-icons/lu';
import { RiCalendarEventLine } from 'react-icons/ri';
import { FiCheck, FiChevronDown, FiCreditCard } from 'react-icons/fi';
import { TbWheelchair } from 'react-icons/tb';
import { FaRegCreditCard } from 'react-icons/fa6';
import { BlueCar, GreenCar, Ev } from '@/icons';
import Image from 'next/image';

export default function RideBookingApp() {
  const [selectedRideType, setSelectedRideType] = useState('EV');
  const [selectedPayment, setSelectedPayment] = useState('Card');
  const [preferences, setPreferences] = useState({
    wheelchair: false,
    quietRide: false,
    petFriendly: false,
  });
  const [automatedReminder, setAutomatedReminder] = useState(true);

  const rideTypes = [
    {
      id: 'EV',
      name: 'EV',
      description: 'Comfy, economical cars',
      icon: Ev,
      color: 'bg-green-100 text-green-600',
    },
    {
      id: 'Sedan',
      name: 'Sedan',
      description: 'Comfy, economical cars',
      icon: BlueCar,
      color: 'bg-blue-100 text-blue-600',
    },
    {
      id: 'SUV',
      name: 'Suv',
      description: 'Comfy, economical cars',
      icon: GreenCar,
      color: 'bg-gray-100 text-gray-600',
    },
  ];

  const preferenceOptions = [
    { id: 'wheelchair', label: 'Wheelchair', icon: TbWheelchair },
    { id: 'quietRide', label: 'Quiet Ride', icon: TbWheelchair },
    { id: 'petFriendly', label: 'Pet-Friendly Ride', icon: TbWheelchair },
  ];

  const togglePreference = pref => {
    setPreferences(prev => ({
      ...prev,
      [pref]: !prev[pref],
    }));
  };

  const [paymentMethod, setPaymentMethod] = useState('Card');
  const fareOptions = ['Fare Estimate', 'Fixed Fare', 'Metered Fare'];
  return (
    <div className="mt-[-15px] pb-6 sm:mt-4 sm:pb-0">
      <div className="flex h-screen flex-col bg-white sm:flex-row">
        {/* Map Section */}
        <div className="relative h-[300px] w-full sm:h-full sm:flex-1">
          <div className="absolute inset-0 rounded-t-2xl bg-gradient-to-br from-blue-100 to-green-100 sm:rounded-t-2xl">
            {/* Map placeholder with locations */}
            <div className="relative h-full w-full overflow-hidden sm:rounded-t-2xl">
              {/* Map background pattern */}
              <div className="absolute inset-0 opacity-20">
                <svg className="h-full w-full" viewBox="0 0 400 600">
                  <defs>
                    <pattern
                      id="mapPattern"
                      x="0"
                      y="0"
                      width="40"
                      height="40"
                      patternUnits="userSpaceOnUse"
                    >
                      <rect width="40" height="40" fill="#f0f9ff" />
                      <path
                        d="M0 40L40 0M-10 10L10 -10M30 50L50 30"
                        stroke="#dbeafe"
                        strokeWidth="1"
                      />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#mapPattern)" />
                </svg>
              </div>

              {/* Location markers */}
              <div className="absolute top-16 left-16 rounded-full bg-white p-2 shadow-lg">
                <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                <div className="absolute -top-2 -right-2 rounded bg-blue-500 px-1 text-xs text-white">
                  De Merenhof - Ons Tweede Thuis
                </div>
              </div>

              <div className="absolute top-32 right-24 rounded-full bg-white p-2 shadow-lg">
                <div className="h-3 w-3 rounded-full bg-gray-400"></div>
                <div className="absolute -top-2 -left-8 rounded bg-gray-500 px-1 text-xs text-white">
                  Rijschool Ton de Haan
                </div>
              </div>

              <div className="absolute bottom-40 left-20 rounded-full bg-white p-2 shadow-lg">
                <div className="h-3 w-3 rounded-full bg-gray-400"></div>
                <div className="absolute -top-2 -right-4 rounded bg-gray-500 px-1 text-xs text-white">
                  Plusschool
                </div>
              </div>

              <div className="absolute right-16 bottom-32 rounded-full bg-white p-2 shadow-lg">
                <div className="h-3 w-3 rounded-full bg-gray-400"></div>
                <div className="absolute -top-2 -left-6 rounded bg-gray-500 px-1 text-xs text-white">
                  S & S Catering
                </div>
              </div>

              <div className="absolute bottom-16 left-32 rounded-full bg-white p-2 shadow-lg">
                <div className="h-3 w-3 rounded-full bg-gray-400"></div>
                <div className="absolute -top-2 -right-8 rounded bg-gray-500 px-1 text-xs text-white">
                  Jeugdtheaterschool Abcoude
                </div>
              </div>

              {/* Current location marker */}
              <div className="absolute top-1/2 left-1/3 -translate-x-1/2 -translate-y-1/2 transform">
                <div className="relative h-6 w-6 rounded-full border-4 border-white bg-blue-600 shadow-lg">
                  <div className="absolute inset-0 animate-ping rounded-full bg-blue-600 opacity-75"></div>
                </div>
              </div>

              {/* Route line */}
              <svg className="absolute inset-0 h-full w-full">
                <path
                  d="M100 150 Q200 250 300 400"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray="10,5"
                  className="opacity-70"
                />
              </svg>

              {/* Destination markers */}
              <div className="absolute right-40 bottom-24 rounded-full bg-blue-600 p-2 text-white shadow-lg">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-white">
                  <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                </div>
                <div className="absolute -top-2 -left-8 rounded bg-blue-600 px-2 py-1 text-xs text-white">
                  PLUS Koot
                </div>
              </div>

              <div className="absolute right-32 bottom-8 rounded-full bg-blue-600 p-2 text-white shadow-lg">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-white">
                  <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                </div>
                <div className="absolute -top-2 -left-6 rounded bg-blue-600 px-2 py-1 text-xs text-white">
                  Albert Heijn
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Panel */}
        <div className="custom-scrollbar mt-6 max-h-[calc(100vh-300px)] w-full overflow-y-auto bg-white px-6 sm:mt-0 sm:max-h-full sm:w-[50%]">
          <div className="space-y-6">
            {/* Location Inputs */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3 rounded-lg border px-5 py-3">
                <span className="text-purple rounded-full bg-blue-100 p-[6px]">
                  <LuMapPin />
                </span>
                <input
                  type="text"
                  placeholder="Pickup location test"
                  className="flex-1 text-[#76787A] outline-none"
                />
              </div>

              <div className="flex items-center space-x-3 rounded-lg border px-5 py-3">
                <span className="text-purple rounded-full bg-blue-100 p-[6px]">
                  <LuFlagTriangleRight />
                </span>
                <input
                  type="text"
                  placeholder="Drop off location"
                  className="flex-1 text-[#76787A] outline-none"
                />
              </div>

              <button className="text-purple text-[15px] font-normal">
                + Add Stop
              </button>
            </div>

            {/* Date & Time */}
            <div className="flex items-center space-x-3 rounded-lg border px-5 py-3">
              <span className="text-purple rounded-full bg-blue-100 p-[6px]">
                <RiCalendarEventLine />
              </span>
              <span className="text-[#76787A]">Select date & time</span>
            </div>

            {/* Ride Type Selection */}
            <div>
              <h3 className="mb-4 text-[16px] font-medium">Select Ride Type</h3>
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-1 md:grid-cols-3">
                {rideTypes.map(type => (
                  <button
                    key={type.id}
                    onClick={() => setSelectedRideType(type.id)}
                    className={`align-center sm:align-left relative flex rounded-lg border p-4 transition-all sm:flex-col ${
                      selectedRideType === type.id
                        ? ''
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {/* Top-right checkbox */}
                    <div className="absolute top-6 right-2 flex h-5 w-5 items-center justify-center rounded-md border border-gray-300 bg-white sm:top-2">
                      {selectedRideType === type.id && (
                        <div className="h-5 w-5 rounded-sm bg-blue-600">
                          {selectedRideType === type.id && (
                            <FiCheck className="text-xs text-white" size={18} />
                          )}
                        </div>
                      )}
                    </div>

                    {/* Icon circle */}
                    <div
                      className={`h-12 w-12 rounded-full ${type.color} flex items-center justify-center sm:mb-4`}
                    >
                      {/* <type.icon size={20} className="w-6 h-6" /> */}
                      <Image
                        src={type.icon}
                        alt="404"
                        className="dark:hidden"
                        width={472}
                        height={152}
                      />
                    </div>

                    {/* Texts */}
                    <div className="pl-5 sm:pl-0">
                      <div className="text-left text-[14px] font-medium">
                        {type.name}
                      </div>
                      <div className="mt-1 text-left text-[14px] text-gray-500">
                        {type.description}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Preferences */}
            <div>
              <h3 className="mb-4 text-[16px] font-medium text-[#050013]">
                Preferences
              </h3>
              <div className="flex flex-wrap gap-3">
                {preferenceOptions.map(option => (
                  <button
                    key={option.id}
                    onClick={() => togglePreference(option.id)}
                    className={`flex items-center space-x-2 rounded-full border px-4 py-2 transition-all ${
                      preferences[option.id]
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 text-gray-600 hover:border-gray-300'
                    }`}
                  >
                    <option.icon size={20} className="text-[#76787A]" />
                    <span className="text-[15px] text-[#050013]">
                      {option.label}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* Automated Reminder */}
            <div className="flex items-center justify-between">
              <span className="text-[16px] font-medium">
                Automated Reminder
              </span>
              <button
                onClick={() => setAutomatedReminder(!automatedReminder)}
                className={`relative h-6 w-12 rounded-full transition-colors ${
                  automatedReminder ? 'bg-[#14EC95]' : 'bg-gray-300'
                }`}
              >
                <div
                  className={`absolute top-0.5 h-5 w-5 rounded-full bg-white shadow-md transition-transform ${
                    automatedReminder ? 'translate-x-6' : 'translate-x-0.5'
                  }`}
                />
              </button>
            </div>

            <div className="bg-tables w-full rounded-2xl p-6">
              {/* Ride Estimation */}
              <div className="mb-6">
                <h2 className="mb-3 text-[18px] font-medium text-[#050013]">
                  Ride Estimation
                </h2>
                <div className="mb-2 flex items-center justify-between">
                  <div className="relative w-[50%] sm:w-[30%]">
                    <select
                      className="appearance-none pr-10 text-[14px] text-[#76787A] focus:ring-2 focus:ring-[#3B00F3] focus:outline-none"
                      defaultValue="Fare Estimate"
                    >
                      {fareOptions.map((option, index) => (
                        <option key={index} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                    <span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 transform rounded-full bg-gray-300 p-[2px] text-lg text-[#050013]">
                      <FiChevronDown />
                    </span>
                  </div>
                  <span className="text-[16px] font-medium text-[#050013]">
                    € 35.00
                  </span>
                </div>
                <div className="flex items-center justify-between space-y-2">
                  <span className="text-[14px] text-[#76787A]">
                    Date & Time
                  </span>
                  <span className="text-[16px] font-medium text-[#050013]">
                    15-02-2025 10:30AM
                  </span>
                </div>
              </div>

              {/* Payment Method */}
              <div className="mb-6">
                <h2 className="mb-3 text-[18px] font-medium text-[#050013]">
                  Payment Method
                </h2>
                <div className="flex flex-col gap-4 sm:flex-row">
                  {['Card', 'Cash'].map(method => (
                    <button
                      key={method}
                      onClick={() => setPaymentMethod(method)}
                      className={`flex w-full items-center justify-between rounded-xl bg-white px-4 py-3 ${
                        paymentMethod === method
                          ? 'shadow-[0px_2px_15px_0px_#1D24610D]'
                          : 'shadow-[0px_2px_15px_0px_#1D24610D]'
                      } transition`}
                    >
                      {/* Left: Icon + Text */}
                      <div className="flex items-center gap-2">
                        <FaRegCreditCard
                          className={`text-lg ${
                            method === 'Card'
                              ? 'text-[#76787A]'
                              : 'text-[#76787A]'
                          }`}
                        />
                        <span className="text-sm font-medium text-[#050013]">
                          {method}
                        </span>
                      </div>

                      {/* Right: Tickbox with check icon if selected */}
                      {paymentMethod === method && (
                        <div className="flex h-5 w-5 items-center justify-center rounded bg-[#3B00F3]">
                          <FiCheck className="text-xs text-white" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Book Ride Button */}
              <button className="w-full rounded-full bg-[#3707EF] py-3 font-medium text-white">
                Book Ride
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
