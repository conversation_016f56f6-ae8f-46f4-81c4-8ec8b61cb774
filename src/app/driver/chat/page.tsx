'use client';
import { useState, useEffect } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { FiPhoneCall } from 'react-icons/fi';
import { TbUser } from 'react-icons/tb';
import { BsFillSendFill } from 'react-icons/bs';
import { HiChevronLeft } from 'react-icons/hi';

const chats = [
  {
    id: '#R12345',
    name: 'J<PERSON>',
    message: "I'm waiting at the main entrance.",
    time: '9:16AM',
    active: true,
  },
  {
    id: 'Dispatcher',
    name: 'D',
    message: 'Please confirm once the pickup is completed',
    time: '9:17AM',
    active: false,
  },
  {
    id: '#R12343',
    name: '<PERSON><PERSON>',
    message: "I'm waiting at the main entrance.",
    date: '18 Jan 2025',
    active: false,
  },
  {
    id: '#R12342',
    name: 'B<PERSON>',
    message: 'Where are you',
    date: '16 Jan 2025',
    active: false,
  },
  {
    id: '#R12340',
    name: '<PERSON><PERSON>',
    message: "I'm waiting at the main entrance.",
    date: '16 Jan 2025',
    active: false,
  },
  {
    id: '#R12339',
    name: '<PERSON>',
    message: "I'm waiting at the main entrance.",
    date: '12 Jan 2025',
    active: false,
  },
];

export default function ChatPage() {
  const { setTitle } = useHeaderTitle();
  const [message, setMessage] = useState('');
  const [selectedChat, setSelectedChat] = useState(null);
  const [showChatWindowOnMobile, setShowChatWindowOnMobile] = useState(false); // ⭐ for mobile toggle

  useEffect(() => {
    setTitle('Chat');
  }, [setTitle]);

  const handleChatClick = chat => {
    setSelectedChat(chat);
    setShowChatWindowOnMobile(true); // 👈 show chat window in mobile
  };

  const handleBackToList = () => {
    setShowChatWindowOnMobile(false); // 👈 go back to chat list in mobile
  };

  return (
    <div className="mx-auto flex h-full max-w-7xl flex-col overflow-hidden rounded-2xl border bg-white sm:h-screen md:flex-row">
      {/* Sidebar - Chat List */}
      <div
        className={`bg-cstm-grey w-full overflow-y-auto border-r p-4 md:block md:w-2/3 ${
          showChatWindowOnMobile ? 'hidden' : 'block'
        }`}
      >
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-sm font-normal">Chat</h2>
        </div>

        <h4 className="mb-2 text-sm text-[#76787A]">Active Chat</h4>
        {chats.slice(0, 2).map((chat, idx) => (
          <div
            key={idx}
            onClick={() => handleChatClick(chat)}
            className={`flex cursor-pointer items-start gap-3 rounded-xl p-3 ${chat.active ? 'bg-green-100' : 'hover:bg-gray-100'}`}
          >
            <div className="relative inline-block">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-sm font-normal text-purple-600">
                {chat.name}
              </div>
              <span className="absolute right-0 bottom-0 h-3 w-3 rounded-full border-2 border-white bg-green-500"></span>
            </div>
            <div className="text-sm">
              <p className="font-semibold">{chat.id}</p>
              <p className="text-xs text-[#76787A]">{chat.message}</p>
            </div>
            <div className="ml-auto text-xs whitespace-nowrap text-[#76787A]">
              {chat.time}
            </div>
          </div>
        ))}

        <h4 className="mt-6 mb-2 text-sm text-[#76787A]">Chat History</h4>
        {chats.slice(2).map((chat, idx) => (
          <div
            key={idx}
            onClick={() => handleChatClick(chat)}
            className="flex cursor-pointer items-start gap-3 rounded-xl p-3 hover:bg-gray-100"
          >
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100 text-sm font-normal text-orange-500">
              {chat.name}
            </div>
            <div className="text-sm">
              <p className="font-semibold text-[#050013]">{chat.id}</p>
              <p className="text-xs text-[#76787A]">{chat.message}</p>
            </div>
            <div className="ml-auto text-xs whitespace-nowrap text-[#76787A]">
              {chat.date}
            </div>
          </div>
        ))}
      </div>

      {/* Chat Window */}
      <div
        className={`flex w-full flex-col md:w-1/3 ${
          !showChatWindowOnMobile && 'hidden md:flex'
        }`}
      >
        {selectedChat ? (
          <>
            {/* Header */}
            <div className="flex items-center justify-between rounded-t-2xl border-b bg-white p-4">
              <div className="flex items-start gap-3">
                {/* Back icon only on mobile */}
                <HiChevronLeft
                  className="mt-1 cursor-pointer text-xl text-gray-500 md:hidden"
                  onClick={handleBackToList}
                />
                <div>
                  <p className="text-sm font-semibold text-[#050013]">
                    {selectedChat.id}
                  </p>
                  <p className="py-1 text-sm font-normal text-[#050013]">
                    {selectedChat.name}
                  </p>
                  <p className="flex items-center gap-1 text-xs text-[#76787A]">
                    Passenger
                    <span className="ml-1 inline-block h-2 w-2 rounded-full bg-green-500"></span>
                    <span className="text-green-600">Online</span>
                  </p>
                </div>
              </div>
              <button className="flex h-8 w-8 items-center justify-center rounded-full border border-blue-500 text-blue-600">
                <FiPhoneCall className="text-sm" />
              </button>
            </div>

            {/* Chat Body */}
            <div className="flex-1 space-y-4 overflow-y-auto p-4">
              <div className="mt-1 mb-6 flex justify-center">
                <span className="bg-cstm-grey rounded-full px-3 py-1 text-[#050013]">
                  Today
                </span>
              </div>

              <div className="flex items-start gap-3">
                <img
                  src="/images/avatar.jpg"
                  alt=""
                  className="h-8 w-8 rounded-full"
                />
                <div>
                  <p className="bg-cstm-grey rounded-xl p-3 text-sm text-[#050013]">
                    Hi {selectedChat.name}! I'm on my way. ETA: 5 minutes.
                  </p>
                  <p className="mt-2 text-xs text-[#76787A]">9:15 AM</p>
                </div>
              </div>

              <div className="mt-6 flex items-end justify-end gap-2">
                <div>
                  <p className="rounded-xl rounded-tr-none bg-gradient-to-r from-indigo-600 to-teal-400 px-4 py-2 text-sm text-white">
                    {selectedChat.message}
                  </p>
                  <p className="mt-2 text-right text-xs text-[#76787A]">
                    {selectedChat.time || selectedChat.date}
                  </p>
                </div>
                <div className="bg-cstm-grey flex h-6 w-6 items-center justify-center rounded-full border">
                  <TbUser />
                </div>
              </div>
            </div>

            {/* Input Box */}
            <div className="space-y-3 p-4">
              <div className="flex flex-wrap gap-2">
                {[
                  "I'll be there in 2 mins",
                  'I have arrived',
                  "Can't find you",
                  'Traffic delay',
                ].map((txt, i) => (
                  <button
                    key={i}
                    className="rounded-full border px-4 py-2 text-sm text-[#050013]"
                  >
                    {txt}
                  </button>
                ))}
              </div>
              <div className="mt-2 flex items-center rounded-full border px-3 py-2">
                <input
                  type="text"
                  placeholder="Type your message..."
                  className="flex-1 border-none bg-transparent text-sm outline-none focus:ring-0"
                  value={message}
                  onChange={e => setMessage(e.target.value)}
                />
                <button className="text-blue-600">
                  <BsFillSendFill className="text-xl" />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex flex-1 items-center justify-center text-sm text-gray-400">
            Select a chat to view messages
          </div>
        )}
      </div>
    </div>
  );
}
