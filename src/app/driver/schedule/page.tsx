'use client';

import React, { useState } from 'react';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import { PiCalendarDot, PiClockCounterClockwiseLight } from 'react-icons/pi';
import { FiMic, FiCamera, FiX } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { TfiCheckBox } from 'react-icons/tfi';
import { IoIosArrowForward, IoIosSearch } from 'react-icons/io';
import {
  TbWheelchair,
  TbMap2,
  TbHelpTriangle,
  TbChevronDown,
  TbMoonStars,
  TbSunHigh,
  TbFilter,
  TbMapPin,
} from 'react-icons/tb';
import {
  LuPhone,
  LuMessageSquareMore,
  LuUser,
  LuFlagTriangleRight,
} from 'react-icons/lu';
import {
  FaPhone,
  FaCommentDots,
  FaUser,
  FaWheelchair,
  FaLocationArrow,
  FaPlus,
} from 'react-icons/fa';
import { GrLocation } from 'react-icons/gr';
import { CiClock2 } from 'react-icons/ci';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { SlArrowLeft } from 'react-icons/sl';
import { LuEuro } from 'react-icons/lu';
import Link from 'next/link';

export default function SchedulePage() {
  const [selectedTrip, setSelectedTrip] = useState('RT2345');
  const [stepSlide, setStepSlide] = useState(0);
  const [availabilityModal, setAvailabilityModal] = useState<boolean>(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const handleDetailView = () => {
    if (window.innerWidth < 640) {
      setIsMobileView(true);
      setStepSlide(0);
    }
  };

  const handleMapView = () => {
    if (window.innerWidth < 640) {
      setIsMobileView(true);
      setStepSlide(1);
    }
  };

  const handleBackToSchedule = () => {
    if (window.innerWidth < 640) {
      setIsMobileView(false);
    }
  };
  React.useEffect(() => {
    const updateView = () =>
      setIsMobileView(window.innerWidth < 640 && isMobileView);
    window.addEventListener('resize', updateView);
    return () => window.removeEventListener('resize', updateView);
  }, [isMobileView]);

  const scheduleData = [
    {
      id: 'RT2345',
      time: '10:00 AM',
      route: 'Singel 230',
      routeId: '#RT2345',
      totalStops: '03',
      passengers: '04',
      date: '24-01-2025',
      vehicle: {
        license: 'XYZ-1234',
        model: 'Toyota Prius',
        type: 'Sedan',
      },
      passengerDetails: [
        ['Henk Maarten', 'Emily Chain'],
        ['Robert Martin', 'Anna Jame'],
      ],
      steps: [
        {
          time: '10:00 AM',
          label: 'Pick Up 1',
          name: 'Henk Maarten',
          address: 'Nieuwezijds Voorburgwal 102',
          icons: [1, 1, 1],
          color: 'bg-green-200 text-green-700',
          dot: 'border-green-500',
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: '10:15 AM',
          label: 'Pick Up 2',
          name: 'Emily Chain',
          address: 'Prinsengracht 78',
          icons: [1],
          color: 'bg-purple-100 text-purple-600',
          dot: 'border-purple-500',
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: '10:30 AM',
          label: 'Drop Off 1',
          name: 'Henk Maarten',
          address: 'Reguliersdwarsstraat 54',
          icons: [1, 1, 1],
          color: 'bg-green-200 text-green-700',
          dot: 'border-green-600',
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: '11:00 AM',
          label: 'Pick Up 3',
          name: 'Robert Martin',
          address: '',
          icons: [1, 1, 1],
          color: 'bg-rose-100 text-rose-400',
          dot: 'border-rose-500',
          passengers: 1,
          wheelchairs: 1,
        },
      ],
    },
    {
      id: 'RT2346',
      time: '12:00 PM',
      route: 'Schiphol Airport',
      routeId: '#RT2346',
      totalStops: '02',
      passengers: '04',
      date: '20-11-2025',
      vehicle: {
        license: 'ABC-98876',
        model: 'Honda Amaze',
        type: 'Sedan',
      },
      passengerDetails: [
        ['John Doe', 'Emily Doe'],
        ['Robert Luthar', 'Ana Jame'],
      ],
      steps: [
        {
          time: '12:03 PM',
          label: 'Pick Up 9',
          name: 'Henry Ford',
          address: 'Portal Street 192',
          icons: [1, 1, 1],
          color: 'bg-green-200 text-green-700',
          dot: 'border-green-500',
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: '10:15 AM',
          label: 'Pick Up 2',
          name: 'Emily Chain',
          address: 'Prinsengracht 78',
          icons: [1],
          color: 'bg-purple-100 text-purple-600',
          dot: 'border-purple-500',
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: '10:30 AM',
          label: 'Drop Off 1',
          name: 'Henk Maarten',
          address: 'Reguliersdwarsstraat 54',
          icons: [1, 1, 1],
          color: 'bg-green-200 text-green-700',
          dot: 'border-green-600',
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: '11:00 AM',
          label: 'Pick Up 3',
          name: 'Robert Martin',
          address: '',
          icons: [1, 1, 1],
          color: 'bg-rose-100 text-rose-400',
          dot: 'border-rose-500',
          passengers: 1,
          wheelchairs: 1,
        },
      ],
    },
  ];

  const selected = scheduleData.find(trip => trip.id === selectedTrip);
  // Availabilty start
  const days = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
  const times = [
    '6 AM',
    '7 AM',
    '8 AM',
    '9 AM',
    '10 AM',
    '11 AM',
    '12 PM',
    '1 PM',
    '2 PM',
    '3 PM',
    '4 PM',
    '5 PM',
    '6 PM',
    '7 PM',
    '8 PM',
  ];

  console.log('Hello There', selected);

  const [rideRequestModal, setRideRequestModal] = useState(false);
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setRideRequestModal(true);
    }, 5000);

    return () => clearTimeout(timer); // cleanup on unmount
  }, []);

  // availability end
  return (
    <div className="min-h-screen">
      <div className="mx-auto max-w-7xl px-6 py-2">
        <TabGroup>
          <TabList className="flex gap-6 bg-white px-6 py-4">
            <Tab
              className={({ selected }) =>
                `flex items-center space-x-2 border-b-2 pb-3 text-sm font-medium outline-none ${
                  selected
                    ? 'text-purple border-blue-600'
                    : 'border-transparent text-[#76787A]'
                }`
              }
            >
              {' '}
              <PiCalendarDot className="h-[20px] w-[20px]" />{' '}
              <span>My Schedule</span>{' '}
            </Tab>
            <Tab
              className={({ selected }) =>
                `flex items-center space-x-2 border-b-2 pb-3 text-sm font-medium outline-none ${
                  selected
                    ? 'text-purple border-blue-600'
                    : 'border-transparent text-[#76787A]'
                }`
              }
            >
              {' '}
              <TfiCheckBox className="h-[20px] w-[20px]" />{' '}
              <span>My Availability</span>{' '}
            </Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              {/* Mobile View Handling */}
              <div className="sm:hidden">
                {/* start test  */}
                <div>
                  <div className="my-6 flex items-center justify-between">
                    <h2 className="text-[16px] font-normal text-[#050013]">
                      My Schedule
                    </h2>
                    <button
                      type="button"
                      className="flex items-center gap-2 rounded-full bg-white px-4 py-2 text-xs font-medium text-[#76787A] shadow-[0px_1px_1px_0px_#0000001A] hover:bg-gray-100 hover:text-blue-700"
                    >
                      <TbFilter size={16} />
                      Filters
                    </button>
                  </div>
                </div>
                <div className="flex items-center gap-4 rounded bg-white px-4 py-3 text-center text-sm shadow-[0px_2px_15px_0px_#1D24610D] sm:gap-0">
                  <div className="bg-cstm-grey text-purple w-full rounded px-6 py-4 text-center font-semibold sm:w-auto">
                    <div className="text-[14px] font-normal text-[#050013]">
                      Today
                    </div>
                    <div className="text-[16px] font-semibold">24 Jan</div>
                  </div>
                  <div className="flex-1 px-6 py-4">
                    <div className="text-[16px] font-semibold text-[#050013]">
                      02
                    </div>
                    <div className="text-[12px] font-normal text-[#76787A]">
                      Rides
                    </div>
                  </div>
                  <div className="flex-1 border-l px-6 py-4">
                    <div className="text-[16px] font-semibold text-[#050013]">
                      08
                    </div>
                    <div className="text-[12px] font-normal text-[#76787A]">
                      Passengers
                    </div>
                  </div>
                </div>
                {/* end test  */}

                {!isMobileView && (
                  <div className="mt-6 space-y-6">
                    {scheduleData.map((trip, idx) => (
                      <div
                        key={idx}
                        onClick={() => {
                          setSelectedTrip(trip.id);
                          handleDetailView();
                        }}
                      >
                        <div
                          key={trip.id}
                          className="mb-4 flex items-start space-x-4"
                        >
                          <div className="w-20 pt-1 text-right text-sm text-[#76787A]">
                            {trip.time}
                          </div>
                          <div
                            onClick={() => setSelectedTrip(trip.id)}
                            className={`flex-1 cursor-pointer rounded-xl border bg-white px-4 py-4 transition sm:px-6 ${
                              selectedTrip === trip.id
                                ? 'border-blue-600 bg-blue-50'
                                : 'border-none hover:border-gray-300'
                            }`}
                          >
                            <div className="mb-4 flex items-center justify-between">
                              <div>
                                <h3 className="mb-2 text-[14px] font-medium text-[#050013]">
                                  {trip.route}
                                </h3>
                                <p className="text-purple text-[14px] font-normal">
                                  {trip.routeId}
                                </p>
                              </div>
                              <div className="bg-cstm-grey flex h-8 w-8 items-center justify-center rounded-full">
                                <IoIosArrowForward className="h-4 w-4 text-gray-800" />
                              </div>
                            </div>
                            <div className="flex flex-wrap items-center justify-between gap-4">
                              <div>
                                <p className="text-[13px] text-[#76787A]">
                                  Total Stops
                                </p>
                                <p className="text-[16px] font-semibold text-[#050013]">
                                  {trip.totalStops}
                                </p>
                              </div>
                              <div>
                                <p className="text-[13px] text-[#76787A]">
                                  Passengers
                                </p>
                                <p className="text-[16px] font-semibold text-[#050013]">
                                  {trip.passengers}
                                </p>
                              </div>
                              <div className="flex items-center">
                                <TbWheelchair
                                  className="pr-2 text-[#76787A]"
                                  size={28}
                                />
                                <button
                                  className={`rounded-full px-4 py-2 text-sm font-medium transition ${
                                    selectedTrip === trip.id
                                      ? 'bg-cstm-blue-700 text-white'
                                      : 'border-btn text-purple border hover:bg-blue-50'
                                  }`}
                                >
                                  View Details
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {isMobileView && stepSlide === 0 && selected && (
                  <div className="mt-6 space-y-4">
                    <div className="flex items-center">
                      <SlArrowLeft
                        onClick={handleBackToSchedule}
                        className="mr-3 cursor-pointer text-xl text-gray-600"
                      />
                      <h2 className="text-lg font-semibold text-[#1F1F1F]">
                        {selected.route}
                      </h2>
                    </div>

                    <div className="custom-scrollbar w-full overflow-y-auto bg-white p-4 sm:p-6">
                      {selected && (
                        <div className="space-y-6">
                          {/* Top Section */}
                          <div className="flex items-start justify-between">
                            <div className="flex">
                              {stepSlide === 1 && (
                                <div className="mr-2 cursor-pointer pt-1 text-[#76787A]">
                                  <SlArrowLeft
                                    onClick={() => setStepSlide(0)}
                                  />
                                </div>
                              )}
                              <div>
                                <h2 className="mb-1 text-[16px] font-medium text-[#050013]">
                                  {selected.route}
                                </h2>
                                <p className="text-purple text-[14px] font-normal">
                                  {selected.routeId}
                                </p>
                              </div>
                            </div>
                            <div className="flex space-x-3">
                              <button
                                onClick={handleMapView}
                                className={`flex h-10 w-10 items-center justify-center rounded-full border text-xl ${
                                  stepSlide === 1
                                    ? 'bg-cstm-blue-700 text-white'
                                    : 'text-purple border-gray-300'
                                }`}
                              >
                                <TbMap2 />
                              </button>
                              <button className="text-purple flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 text-xl">
                                <TbHelpTriangle />
                              </button>
                            </div>
                          </div>

                          {/* Meta Info */}
                          <div className="flex flex-wrap gap-x-4 gap-y-2 border-b pb-4 text-[13px] text-gray-700">
                            <div className="text-[#76787A]">
                              Date:{' '}
                              <span className="font-medium text-[#050013]">
                                {selected.date} {selected.time}
                              </span>
                            </div>
                            <div className="border-l pl-2 text-[#76787A]">
                              Stops:{' '}
                              <span className="font-medium text-[#050013]">
                                {selected.totalStops}
                              </span>
                            </div>
                            <div className="flex items-center gap-1 border-l pl-2 text-[#76787A]">
                              Passengers:
                              <span className="inline-flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-[#18EC95] to-[#3324E3] text-xs text-[11px] text-white">
                                {selected.passengers}
                              </span>
                            </div>
                          </div>

                          {/* Vehicle Details */}
                          <div className="space-y-4 rounded-xl bg-gray-50 p-6">
                            <h3 className="text-[13px] font-medium text-[#76787A]">
                              Vehicle Details
                            </h3>
                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-700 sm:grid-cols-3">
                              <div>
                                <span className="text-[#76787A]">
                                  License Plate:
                                </span>
                                <br />
                                <span className="font-medium text-[#050013]">
                                  "License"
                                </span>
                              </div>
                              <div>
                                <span className="text-[#76787A]">Model:</span>
                                <br />
                                <span className="font-medium text-[#050013]">
                                  model
                                </span>
                              </div>
                              <div>
                                <span className="text-[#76787A]">Type:</span>
                                <br />
                                <span className="font-medium text-[#050013]">
                                  type
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Passenger Details */}
                          <div className="rounded-xl bg-gray-50 px-6 py-3">
                            <h3 className="mb-5 text-[13px] font-medium text-[#76787A]">
                              Passenger Details
                            </h3>
                            <div className="grid grid-cols-1 gap-y-3 text-sm text-gray-800 sm:grid-cols-2">
                              {selected.passengerDetails
                                .flat()
                                .map((name, i) => (
                                  <div
                                    key={i}
                                    className="flex items-center justify-around"
                                  >
                                    <span className="text-[#050013]">
                                      {name}
                                    </span>
                                    <div className="flex space-x-2">
                                      <LuPhone className="text-[#76787A]" />
                                      <LuMessageSquareMore className="text-[#76787A]" />
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>

                          {/* Stepper */}
                          <div className="space-y-10">
                            {selected.steps.map((step, index) => (
                              <div key={index} className="relative flex gap-4">
                                <div className="hidden w-[80px] pt-1 text-right text-xs text-gray-500 sm:block">
                                  <div className="text-[13px] text-[#76787A]">
                                    {step.time}
                                  </div>
                                  <div className="text-[12px] text-[#050013]">
                                    {step.type || 'Depart'}
                                  </div>
                                </div>
                                <div className="relative flex min-h-[60px] flex-col items-center">
                                  <div
                                    className={`h-4 w-4 rounded-full border-4 bg-white ${step.dot}`}
                                  />
                                  {index !== selected.steps.length - 1 && (
                                    <div className="absolute top-4 left-1/2 h-[115px] w-px -translate-x-1/2 transform border-l border-dashed border-gray-300" />
                                  )}
                                </div>
                                <div className="flex-1">
                                  <div
                                    className={`mb-1 inline-block rounded-full px-3 py-1.5 text-xs font-medium ${step.color}`}
                                  >
                                    {step.label}
                                  </div>
                                  <div className="mt-2 flex items-center gap-4 text-[14px] font-medium text-[#050013]">
                                    {step.name}
                                    {step.passengers !== undefined && (
                                      <span className="flex items-center gap-1 text-[13px] font-normal text-[#050013]">
                                        <LuUser className="text-xs" />{' '}
                                        {step.passengers}
                                      </span>
                                    )}
                                    {step.wheelchairs !== undefined && (
                                      <span className="flex items-center gap-1 text-[13px] font-normal text-[#050013]">
                                        <TbWheelchair className="text-xs" />{' '}
                                        {step.wheelchairs}
                                      </span>
                                    )}
                                  </div>
                                  {step.address && (
                                    <div className="mt-1 flex items-center gap-1 text-[12px] text-[#030015]">
                                      <span className="mr-[5px] rounded-full bg-blue-50 p-[6px] text-[14px]">
                                        <GrLocation className="text-purple" />
                                      </span>
                                      {step.address}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {isMobileView && stepSlide === 1 && (
                  <div className="space-y-4">
                    <div>
                      {/* <SlArrowLeft
          onClick={() => setStepSlide(0)}
          className="text-xl text-gray-600 mr-3 cursor-pointer"
        /> */}
                      {/* map view start  */}
                      <div className="mt-6 flex items-start justify-between">
                        <div className="flex">
                          <SlArrowLeft
                            onClick={() => setStepSlide(0)}
                            className="mr-3 cursor-pointer text-xl text-gray-600"
                          />
                          <div>
                            <h2 className="mb-1 text-[16px] font-medium text-[#050013]">
                              {selected.route}
                            </h2>
                            <p className="text-purple text-[14px] font-normal">
                              {selected.routeId}
                            </p>
                          </div>
                        </div>
                        <div className="flex space-x-3">
                          <button
                            onClick={handleMapView}
                            className={`flex h-10 w-10 items-center justify-center rounded-full border text-xl ${
                              stepSlide === 1
                                ? 'bg-cstm-blue-700 text-white'
                                : 'text-purple border-gray-300'
                            }`}
                          >
                            <TbMap2 />
                          </button>
                          <button className="text-purple flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 text-xl">
                            <TbHelpTriangle />
                          </button>
                        </div>
                      </div>
                      <div className="mt-5 flex flex-wrap gap-x-4 gap-y-2 border-b pb-4 text-[13px] text-gray-700">
                        <div className="text-[#76787A]">
                          Date:{' '}
                          <span className="font-medium text-[#050013]">
                            {selected.date} {selected.time}
                          </span>
                        </div>
                        <div className="border-l pl-2 text-[#76787A]">
                          Stops:{' '}
                          <span className="font-medium text-[#050013]">
                            {selected.totalStops}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 border-l pl-2 text-[#76787A]">
                          Passengers:
                          <span className="inline-flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-[#18EC95] to-[#3324E3] text-xs text-[11px] text-white">
                            {selected.passengers}
                          </span>
                        </div>
                      </div>
                      {/* map view end  */}
                    </div>

                    <div className="h-[400px] w-full overflow-hidden rounded-2xl border border-gray-200 shadow">
                      <img
                        src="/images/map.jpg"
                        alt="Map"
                        className="h-full w-full object-cover"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Desktop View */}
              <div className="hidden sm:block">
                <div className="relative h-screen w-full overflow-hidden rounded-[15px] border bg-gray-100">
                  <div
                    className="flex h-full transition-transform duration-700 ease-in-out"
                    style={{
                      width: '150%',
                      transform: `translateX(-${stepSlide * 33.3333}%)`,
                    }}
                  >
                    {/* Schedule Panel */}
                    <div className="bg-tables custom-scrollbar w-full overflow-y-auto p-4 sm:p-6">
                      <div className="mb-6 flex items-center justify-between">
                        <h2 className="text-[16px] font-normal text-[#050013]">
                          My Schedule
                        </h2>
                        <button
                          type="button"
                          className="flex items-center gap-2 rounded-full bg-white px-4 py-2 text-xs font-medium text-[#76787A] shadow-[0px_1px_1px_0px_#0000001A] hover:bg-gray-100 hover:text-blue-700"
                        >
                          <TbFilter size={16} />
                          Filters
                        </button>
                      </div>

                      {/* Stats */}
                      <div className="mb-6 flex flex-col items-center gap-4 rounded bg-white p-2 text-center text-sm shadow-[0px_2px_15px_0px_#1D24610D] sm:flex-row sm:gap-0">
                        <div className="bg-cstm-grey text-purple w-full rounded px-6 py-4 text-center font-semibold sm:w-auto">
                          <div className="text-[14px] font-normal text-[#050013]">
                            Today
                          </div>
                          <div className="text-[16px] font-semibold">
                            24 Jan
                          </div>
                        </div>
                        <div className="flex-1 px-6 py-4">
                          <div className="text-[16px] font-semibold text-[#050013]">
                            02
                          </div>
                          <div className="text-[12px] font-normal text-[#76787A]">
                            Rides
                          </div>
                        </div>
                        <div className="h-6 border-t sm:border-t-0 sm:border-l"></div>
                        <div className="flex-1 px-6 py-4">
                          <div className="text-[16px] font-semibold text-[#050013]">
                            08
                          </div>
                          <div className="text-[12px] font-normal text-[#76787A]">
                            Passengers
                          </div>
                        </div>
                      </div>

                      {/* Trip List */}
                      {scheduleData.map(trip => (
                        <div
                          key={trip.id}
                          className="mb-4 flex items-start space-x-4"
                        >
                          <div className="hidden w-20 pt-1 text-right text-sm text-[#76787A] sm:block">
                            {trip.time}
                          </div>
                          <div
                            onClick={() => setSelectedTrip(trip.id)}
                            className={`flex-1 cursor-pointer rounded-xl border bg-white px-4 py-4 transition sm:px-6 ${
                              selectedTrip === trip.id
                                ? 'border-blue-600 bg-blue-50'
                                : 'border-none hover:border-gray-300'
                            }`}
                          >
                            <div className="mb-4 flex items-center justify-between">
                              <div>
                                <h3 className="mb-2 text-[14px] font-medium text-[#050013]">
                                  {trip.route}
                                </h3>
                                <p className="text-purple text-[14px] font-normal">
                                  {trip.routeId}
                                </p>
                              </div>
                              <div className="bg-cstm-grey flex h-8 w-8 items-center justify-center rounded-full">
                                <IoIosArrowForward className="h-4 w-4 text-gray-800" />
                              </div>
                            </div>
                            <div className="flex flex-wrap items-center justify-between gap-4">
                              <div>
                                <p className="text-[13px] text-[#76787A]">
                                  Total Stops
                                </p>
                                <p className="text-[16px] font-semibold text-[#050013]">
                                  {trip.totalStops}
                                </p>
                              </div>
                              <div>
                                <p className="text-[13px] text-[#76787A]">
                                  Passengers
                                </p>
                                <p className="text-[16px] font-semibold text-[#050013]">
                                  {trip.passengers}
                                </p>
                              </div>
                              <div className="flex items-center">
                                <TbWheelchair
                                  className="pr-2 text-[#76787A]"
                                  size={28}
                                />
                                <button
                                  className={`rounded-full px-4 py-2 text-[13px] font-medium transition ${
                                    selectedTrip === trip.id
                                      ? 'bg-cstm-blue-700 text-white'
                                      : 'border-btn text-purple border hover:bg-blue-50'
                                  }`}
                                >
                                  View Details
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Detail Panel */}
                    <div className="w-full bg-white">
                      {selected && (
                        <div className="space-y-6">
                          {/* Top Section */}
                          <div className="flex items-start justify-between px-6 pt-6">
                            <div className="flex">
                              {stepSlide === 1 && (
                                <div className="mr-2 cursor-pointer pt-1 text-[#76787A]">
                                  <SlArrowLeft
                                    onClick={() => setStepSlide(0)}
                                  />
                                </div>
                              )}
                              <div>
                                <h2 className="mb-1 text-[16px] font-medium text-[#050013]">
                                  {selected.route}
                                </h2>
                                <p className="text-purple text-[14px] font-normal">
                                  {selected.routeId}
                                </p>
                              </div>
                            </div>
                            <div className="flex space-x-3">
                              <button
                                onClick={() => setStepSlide(1)}
                                className={`flex h-10 w-10 items-center justify-center rounded-full border text-xl ${
                                  stepSlide === 1
                                    ? 'bg-cstm-blue-700 text-white'
                                    : 'text-purple border-gray-300'
                                }`}
                              >
                                <TbMap2 />
                              </button>
                              <button className="text-purple flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 text-xl">
                                <TbHelpTriangle />
                              </button>
                            </div>
                          </div>

                          {/* Meta Info */}
                          <div className="flex flex-wrap gap-x-4 gap-y-2 border-b px-6 pb-6 text-[13px] text-gray-700">
                            <div className="text-[#76787A]">
                              Date:{' '}
                              <span className="font-medium text-[#050013]">
                                {selected.date} {selected.time}
                              </span>
                            </div>
                            <div className="h-4 border-l pl-2 text-[#76787A]">
                              Stops:{' '}
                              <span className="font-medium text-[#050013]">
                                {selected.totalStops}
                              </span>
                            </div>
                            <div className="flex h-4 items-center gap-2 border-l pl-2 text-[#76787A]">
                              Passengers:
                              <span className="inline-flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-[#18EC95] to-[#3324E3] text-xs text-white">
                                {selected.passengers}
                              </span>
                            </div>
                          </div>

                          <div className="custom-scrollbar h-[550px] overflow-y-auto px-6">
                            {/* Vehicle Details */}
                            <div className="space-y-4 rounded-xl bg-gray-50 p-6">
                              <h3 className="rounded-full bg-white px-5 py-3 text-[13px] font-medium text-[#76787A]">
                                Vehicle Details
                              </h3>
                              <div className="grid grid-cols-2 gap-4 px-6 text-sm text-gray-700 sm:grid-cols-3">
                                <div>
                                  <span className="text-[#76787A]">
                                    License Plate:
                                  </span>
                                  <br />
                                  <span className="font-medium text-[#050013]">
                                    "License"
                                  </span>
                                </div>
                                <div>
                                  <span className="text-[#76787A]">Model:</span>
                                  <br />
                                  <span className="font-medium text-[#050013]">
                                    model
                                  </span>
                                </div>
                                <div>
                                  <span className="text-[#76787A]">Type:</span>
                                  <br />
                                  <span className="font-medium text-[#050013]">
                                    type
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Passenger Details */}
                            <div className="rounded-xl bg-gray-50 px-6 pb-6">
                              <h3 className="rounded-full bg-white px-5 py-3 text-[13px] font-medium text-[#76787A]">
                                Passenger Details
                              </h3>
                              <div className="mt-5 grid grid-cols-1 gap-y-3 text-sm text-gray-800 sm:grid-cols-2">
                                {selected.passengerDetails
                                  .flat()
                                  .map((name, i) => (
                                    <div
                                      key={i}
                                      className="flex items-center justify-around"
                                    >
                                      <span className="text-[#050013]">
                                        {name}
                                      </span>
                                      <div className="flex space-x-2">
                                        <LuPhone className="hover:text-purple cursor-pointer text-[#76787A]" />
                                        <LuMessageSquareMore className="hover:text-purple cursor-pointer text-[#76787A]" />
                                      </div>
                                    </div>
                                  ))}
                              </div>
                            </div>

                            {/* Stepper */}
                            <div className="mt-6 space-y-10">
                              {selected.steps.map((step, index) => (
                                <div
                                  key={index}
                                  className="relative flex gap-4"
                                >
                                  <div className="hidden w-[80px] pt-1 text-right text-xs text-gray-500 sm:block">
                                    <div className="text-[13px] text-[#76787A]">
                                      {step.time}
                                    </div>
                                    <div className="text-[12px] text-[#050013]">
                                      {step.type || 'Depart'}
                                    </div>
                                  </div>
                                  <div className="relative flex min-h-[60px] flex-col items-center">
                                    <div
                                      className={`h-4 w-4 rounded-full border-4 bg-white ${step.dot}`}
                                    />
                                    {index !== selected.steps.length - 1 && (
                                      <div className="absolute top-4 left-1/2 h-[115px] w-px -translate-x-1/2 transform border-l border-dashed border-gray-300" />
                                    )}
                                  </div>
                                  <div className="flex-1">
                                    <div
                                      className={`mb-1 inline-block rounded-full px-3 py-1.5 text-xs font-medium ${step.color}`}
                                    >
                                      {step.label}
                                    </div>
                                    <div className="mt-2 flex items-center gap-4 text-[14px] font-medium text-[#050013]">
                                      {step.name}
                                      {step.passengers !== undefined && (
                                        <span className="flex items-center gap-1 text-[13px] font-normal text-[#050013]">
                                          <LuUser className="text-xs" />{' '}
                                          {step.passengers}
                                        </span>
                                      )}
                                      {step.wheelchairs !== undefined && (
                                        <span className="flex items-center gap-1 text-[13px] font-normal text-[#050013]">
                                          <TbWheelchair className="text-xs" />{' '}
                                          {step.wheelchairs}
                                        </span>
                                      )}
                                    </div>
                                    {step.address && (
                                      <div className="mt-1 flex items-center gap-1 text-[12px] text-[#030015]">
                                        <span className="mr-[5px] rounded-full bg-blue-50 p-[6px] text-[14px]">
                                          <GrLocation className="text-purple" />
                                        </span>
                                        {step.address}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Map Panel */}
                    <div className="flex h-full w-full items-center justify-center bg-white p-4">
                      <img
                        src="/images/map.jpg"
                        alt="Map"
                        className="h-full w-full rounded-xl object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabPanel>

            <TabPanel>
              <div className="rounded-xl border border-gray-300">
                {/* Filter Bar */}
                <div className="rounded-xl bg-white pt-4">
                  {/* Top Row */}
                  <div className="mb-4 flex flex-wrap items-center justify-between gap-4 px-4">
                    {/* Left Section: Heading + Week Dropdown + Date Picker */}
                    <div className="flex flex-wrap items-center gap-3">
                      <h2 className="text-[16px] font-normal text-[#050013]">
                        Shift
                      </h2>

                      {/* Week Dropdown */}
                      <select className="rounded-full border border-gray-300 bg-white px-5 py-1.5 text-sm text-gray-700 focus:outline-none">
                        <option>Week</option>
                        <option>Month</option>
                        <option>Day</option>
                      </select>

                      {/* Date Picker */}
                      <div className="flex items-center rounded-full border border-gray-300 bg-white px-4 py-2 text-sm text-gray-700">
                        <span className="mr-2">Jan 20 - Jan 27</span>
                        <span className="text-gray-400">2025</span>
                      </div>
                    </div>

                    {/* Right Section: Night/Day Shift Buttons + Add */}
                    <div className="flex flex-wrap items-center gap-3">
                      <button className="flex items-center gap-2 rounded-full bg-[#F1EDFF] px-4 py-2 text-sm font-normal text-[#050013]">
                        Night Shift{' '}
                        <TbMoonStars className="text-btn text-[16px]" />
                      </button>
                      <button className="flex items-center gap-2 rounded-full bg-[#FFF7E6] px-4 py-2 text-sm font-normal text-[#050013]">
                        Day Shift{' '}
                        <TbSunHigh className="text-[16px] text-[#FFB200]" />
                      </button>

                      <button
                        className="bg-cstm-blue-700 flex items-center gap-2 rounded-full px-3 py-2 text-sm font-medium text-white transition"
                        onClick={() => setAvailabilityModal(true)}
                      >
                        <FaPlus className="text-sm" />
                        <span className="sm:hidden">Add</span>{' '}
                        {/* Small screen */}
                        <span className="hidden sm:inline">
                          Add Availability
                        </span>{' '}
                        {/* Large screen */}
                      </button>
                    </div>
                  </div>

                  {/* Second Row: Search + Filter/Refresh Buttons */}
                  {/* Search + Buttons Area */}
                  <div className="bg-tables flex flex-wrap items-center justify-between gap-3 p-3 sm:flex-nowrap">
                    {/* Search Bar */}
                    <form className="flex-1">
                      <label className="sr-only">Search</label>
                      <div className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <IoIosSearch className="h-[20px] w-[20px] text-[#050013]" />
                        </div>
                        <input
                          type="search"
                          className="w-full rounded-full border border-gray-300 p-2 pl-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 sm:w-[300px]"
                          placeholder="Search here"
                          onChange={e => setSearchTerm(e.target.value)}
                        />
                      </div>
                    </form>

                    {/* Filter + Refresh Buttons */}
                    <div className="flex items-center gap-2">
                      {/* Filter Button */}
                      <button
                        type="button"
                        className="flex items-center gap-2 rounded-full bg-white px-4 py-2 text-xs font-medium text-[#76787A] shadow-[0px_1px_1px_0px_#0000001A] hover:bg-gray-100 hover:text-blue-700"
                      >
                        {/* Icon */}
                        <TbFilter size={16} />

                        {/* Label */}
                        <span className="hidden sm:inline">Filters</span>
                      </button>

                      {/* Refresh Button */}
                      <button
                        type="button"
                        aria-label="refresh"
                        className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700"
                      >
                        <PiClockCounterClockwiseLight size={20} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Static Calendar */}
                <div className="overflow-x-auto rounded-xl shadow-sm">
                  <div className="grid min-w-[900px] grid-cols-8">
                    <div className="border-r bg-[#FAFAFA]"></div>
                    {days.map(day => (
                      <div
                        key={day}
                        className="border-r border-b bg-[#FAFAFA] py-3 text-center text-sm font-semibold text-[#76787A]"
                      >
                        {day}
                      </div>
                    ))}
                    {times.map(time => (
                      <React.Fragment key={time}>
                        <div className="border-r border-b bg-[#FAFAFA] pt-4 pr-2 text-right text-xs text-[#76787A]">
                          {time}
                        </div>
                        {days.map(day => (
                          <div
                            key={day + time}
                            className="min-h-[60px] border-r border-b p-1"
                          >
                            {day === 'MON' && time === '6 AM' && (
                              <div className="rounded-md bg-[#F3F0FF] px-3 py-1 text-xs font-medium text-[#6F3FF5]">
                                <TbMoonStars /> Night Shift
                              </div>
                            )}
                            {day === 'THU' && time === '6 AM' && (
                              <div className="rounded-md bg-[#FFF0F0] px-3 py-1 text-xs font-medium text-[#D12953]">
                                On Leave
                              </div>
                            )}
                            {day === 'SAT' && time === '8 AM' && (
                              <div className="rounded-md bg-[#FFF3D1] px-3 py-1 text-xs font-medium text-[#A96B00]">
                                Day Shift ☀️
                              </div>
                            )}
                          </div>
                        ))}
                      </React.Fragment>
                    ))}
                  </div>
                </div>
              </div>
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
      {availabilityModal && (
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
          <div className="absolute bottom-0 flex h-[500px] w-full translate-x-0 transform flex-col justify-between overflow-y-scroll rounded-l-2xl bg-white shadow-lg transition-all duration-500 sm:relative sm:h-full sm:max-w-md sm:translate-x-0 sm:overflow-visible">
            <div>
              <div className="bg-cstm-grey mb-6 flex items-center justify-between rounded-tl-[18px] p-6">
                <h3 className="text-lg font-normal text-[#050013]">
                  Add Availability
                </h3>
                <button
                  onClick={() => setAvailabilityModal(false)}
                  className="text-xl text-gray-400 hover:text-black"
                >
                  <FiX />
                </button>
              </div>

              <div className="space-y-4 p-6">
                <div className="space-y-5 text-sm font-medium text-[#111827]">
                  {/* Select Shift */}
                  <select className="w-full rounded-lg border px-4 py-3 text-[13px] font-normal text-[#76787A] focus:ring-2 focus:ring-[#6F3FF5] focus:outline-none">
                    <option>Select Shift</option>
                    <option>Morning</option>
                    <option>Evening</option>
                  </select>

                  {/* Date Range Picker */}
                  <div className="relative">
                    <DatePicker
                      className="w-full rounded-lg border px-4 py-3 text-[13px] font-normal text-[#050013] placeholder-[#76787A]"
                      showYearDropdown
                      showMonthDropdown
                      scrollableYearDropdown
                      dateFormat="dd/MM/yyyy"
                      placeholderText="Select Date"
                      minDate={new Date()}
                    />
                    <span className="absolute top-[10px] right-4 text-[20px] text-[#76787A]">
                      <PiCalendarDot className="text-[#76787A]" />
                    </span>
                  </div>

                  {/* Start & End Time */}
                  <div className="flex gap-4">
                    <div className="relative w-full">
                      <input
                        type="text"
                        placeholder="Start time"
                        className="w-full rounded-lg border px-4 py-3 text-[13px] font-normal text-[#050013] placeholder-[#76787A] focus:outline-none"
                      />
                      <span className="absolute top-[10px] right-4 text-[20px] text-[#76787A]">
                        <CiClock2 />
                      </span>
                    </div>
                    <div className="relative w-full">
                      <input
                        type="text"
                        placeholder="End time"
                        className="w-full rounded-lg border px-4 py-3 text-[13px] font-normal text-[#050013] placeholder-[#76787A] focus:outline-none"
                      />
                      <span className="absolute top-[10px] right-4 text-[20px] text-[#76787A]">
                        <CiClock2 />
                      </span>
                    </div>
                  </div>

                  {/* Add Leave Section */}
                  <div className="space-y-3 pt-2">
                    <p className="text-[14px] font-medium text-[#050013]">
                      Add leave
                    </p>

                    <div className="relative">
                      {/* <input
            type="text"
            placeholder="Select date"
            className="w-full border rounded-lg px-4 py-3 text-sm text-[#050013] placeholder-[#76787A] font-normal focus:outline-none"
          /> */}
                      <DatePicker
                        className="w-full rounded-lg border px-4 py-3 text-[13px] font-normal text-[#050013] placeholder-[#76787A]"
                        showYearDropdown
                        showMonthDropdown
                        scrollableYearDropdown
                        dateFormat="dd/MM/yyyy"
                        placeholderText="Select Date"
                        minDate={new Date()}
                      />
                      <span className="absolute top-[10px] right-4 text-[20px] text-[#76787A]">
                        <PiCalendarDot className="text-[#76787A]" />
                      </span>
                    </div>

                    <textarea
                      placeholder="Description"
                      className="w-full resize-none rounded-lg border px-4 py-3 text-sm font-normal text-[#050013] placeholder-[#76787A] focus:outline-none"
                      rows={2}
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end border-t p-6">
              <button className="bg-cstm-blue-700 flex items-center gap-5 rounded-full px-6 py-2 text-sm font-medium text-white transition">
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* automatic modal start  */}
      {rideRequestModal && (
        <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black/30">
          <div className="relative w-[90%] max-w-sm rounded-2xl bg-white shadow-xl">
            {/* Close Button */}
            <button
              onClick={() => setRideRequestModal(false)}
              className="absolute -top-[7%] right-0 text-lg text-gray-400 hover:text-gray-700"
            >
              <FiX />
            </button>

            {/* Heading */}
            <h2 className="px-6 pt-6 text-[15px] font-normal text-[#050013]">
              New Ride Request
            </h2>

            {/* Price + Countdown Circle */}
            <div className="mb-6 flex items-center justify-between border-b px-6 pb-3">
              <div className="text-[24px] font-medium text-[#050013]">
                € 35.00
              </div>
              <div className="relative flex h-14 w-14 items-center justify-center">
                <div className="absolute h-full w-full rounded-full border-[3px] border-[#D4FFE6]"></div>

                <div className="animate-spin-slow absolute h-full w-full rounded-full border-[3px] border-t-[#24DA96] border-r-transparent border-b-transparent border-l-transparent"></div>

                <span className="z-10 text-xs font-semibold text-[#050013]">
                  05:10
                </span>
              </div>
            </div>

            {/* Address and Details */}
            <div className="space-y-4 px-6 pb-6 text-sm">
              <div className="flex items-start gap-3">
                <div className="flex flex-col items-center">
                  {/* First Icon */}
                  <div className="text-purple rounded-full bg-[#F0EFFF] p-1">
                    <TbMapPin />
                  </div>

                  {/* Dashed Line Centered */}
                  <div className="my-1 h-5 self-center border-l border-dashed border-gray-400"></div>

                  {/* Second Icon */}
                  <div className="text-purple rounded-full bg-[#F0EFFF] p-1">
                    <LuFlagTriangleRight />
                  </div>
                </div>

                <div className="flex-1 space-y-6">
                  <div className="flex justify-between">
                    <p className="text-[12px] font-normal text-[#050013]">
                      288-292, Spuistraat City
                    </p>
                    <p className="text-[12px] font-normal text-[#76787A]">
                      1 Hr. 15 min
                    </p>
                    <p className="text-[12px] font-normal text-[#050013]">
                      18km
                    </p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-[12px] font-normal text-[#050013]">
                      1000 AP, Schipol Airport
                    </p>
                    <p className="text-[12px] font-normal text-[#76787A]">
                      2 Hr. 15 min
                    </p>
                    <p className="text-[12px] font-normal text-[#050013]">
                      45km
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Accept Button */}
            <div className="border-t px-6 pt-3 pb-6">
              <div className="mt-2 flex items-center justify-between px-1">
                <p className="text-[12px] font-normal text-[#050013]">
                  This ride has 5 Stops
                </p>
                <div className="flex items-center gap-2 text-[12px] font-medium text-[#18EC95]">
                  <span className="border-green rounded-full border p-1">
                    <LuEuro />
                  </span>
                  Pre Paid
                </div>
              </div>
              <Link href="/driver/start-ride">
                <button className="mt-4 w-full rounded-full bg-[#3707EF] py-3 text-base text-[14px] font-semibold text-white shadow">
                  Accept
                </button>
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* automatic modal end  */}
    </div>
  );
}
