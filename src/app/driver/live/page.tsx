'use client';

import { useEffect } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { IoCloseOutline } from 'react-icons/io5';
import { GoArrowUp } from 'react-icons/go';
import { GrDirections } from 'react-icons/gr';
import { CgCornerDoubleUpLeft } from 'react-icons/cg';

export default function StartRidePage() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle('Start Ride');
  }, []);

  return (
    <div className="mx-auto max-w-7xl">
      <div className="flex flex-col overflow-hidden bg-white shadow-lg md:flex-row">
        {/* Right Panel: Map */}
        <div className="relative flex w-full items-center justify-center bg-gray-100">
          {/* Map Image */}
          <img
            src="/images/map.svg"
            alt="Route Map"
            className="h-full w-full object-cover"
          />

          {/* Top Left: Direction */}
          <div className="absolute top-4 left-4 flex items-center gap-2 rounded-xl bg-white px-6 py-5 text-sm shadow-md">
            <GoArrowUp className="text-[#050013]" size={22} />
            <div className="flex flex-col leading-tight">
              <span className="text-[13px] font-normal text-[#76787A]">
                Towards
              </span>
              <span className="text-[15px] font-normal text-[#050013]">
                Spuistraat City Rd
              </span>
            </div>
          </div>

          {/* Then Button */}
          <div className="absolute top-[28%] left-4 sm:top-[12%]">
            <button className="flex items-center gap-2 rounded-full bg-black px-5 py-2 text-[13px] text-white shadow-md">
              <CgCornerDoubleUpLeft size={25} />
              Then
            </button>
          </div>

          <div className="absolute bottom-0 left-0 flex w-full items-center justify-evenly rounded-none bg-white px-4 py-3 shadow-lg sm:top-[20%] sm:bottom-auto sm:left-4 sm:w-auto sm:justify-start sm:gap-5 sm:rounded-xl">
            <IoCloseOutline
              className="rounded-full border p-[5px] text-[#050013]"
              size={40}
            />
            <div className="text-left">
              <p className="text-btn text-[24px] leading-tight font-semibold">
                35 min
              </p>
              <p className="text-[15px] text-[#76787A]">21 Km • 14:42</p>
            </div>
            <GrDirections
              className="ml-2 rounded-full border p-[10px] text-[#050013]"
              size={40}
            />
          </div>

          <div className="fixed bottom-[10%] left-4 flex items-center gap-5 rounded-full bg-white p-3 shadow-lg sm:absolute">
            <div className="text-center text-[13px]">
              <p className="text-btn leading-tight font-semibold">--</p>
              <p className="text-[#76787A]">Km/h</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
