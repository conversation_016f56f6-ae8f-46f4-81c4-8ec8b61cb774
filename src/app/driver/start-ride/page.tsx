'use client';

import { useEffect, useState } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { FaStar } from 'react-icons/fa';
import { FiChevronDown } from 'react-icons/fi';
import { PiMapPinLight } from 'react-icons/pi';
import { RxStarFilled } from 'react-icons/rx';
import { LuFlagTriangleRight } from 'react-icons/lu';

export default function StartRidePage() {
  const { setTitle } = useHeaderTitle();
  const [isRightPanel, setIsRightPanel] = useState<boolean>(false);

  useEffect(() => {
    setTitle('Start Ride');
  }, []);

  return (
    <div className="mx-auto max-w-7xl sm:p-4">
      <div className="flex flex-col overflow-hidden rounded-2xl border bg-white md:flex-row">
        {/* Left Panel */}
        <div className="relative hidden w-full space-y-6 sm:block md:w-1/2">
          <div className="space-y-4 p-6">
            <div className="flex items-start gap-3">
              <img
                src="/images/avatar.jpg"
                alt="Rider"
                className="h-12 w-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-2">
                  <p className="text-[16px] font-medium text-[#050013]">
                    Rivka Frank
                  </p>
                  <div className="flex items-center text-[15px] text-yellow-500">
                    <RxStarFilled />
                    <span className="ml-1 text-[15px] font-normal text-[#050013]">
                      4.5
                    </span>
                  </div>
                </div>

                <div className="relative w-40">
                  <select className="appearance-none rounded-lg border-0 text-[12px] text-[#76787A] focus:outline-none">
                    <option>Fare Estimate</option>
                    <option>Premium</option>
                  </select>
                  <span className="bg-tables pointer-events-none absolute top-[14px] right-[30%] -translate-y-1/2 rounded-[12px] p-[2px]">
                    <FiChevronDown className="text-[#050013]" />
                  </span>
                </div>

                <div>
                  <p className="text-[20px] font-medium text-[#050013]">
                    € 35.00
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t p-6">
            <div className="relative">
              <p className="mb-4 text-[13px] font-normal text-[#76787A]">
                This ride has 5 Stops
              </p>
              <div className="space-y-0">
                <div className="relative flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <PiMapPinLight
                      className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                      size={25}
                    />
                    <div className="h-6 border-l-2 border-dashed border-gray-300" />
                  </div>
                  <p className="text-[13px] font-normal text-[#050013]">
                    288–292, Spuistraat City
                  </p>
                </div>

                <div className="relative flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <LuFlagTriangleRight
                      className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                      size={25}
                    />
                  </div>
                  <p className="text-[13px] font-normal text-[#050013]">
                    1000 AP, Schipol Airport...
                  </p>
                </div>
              </div>

              <div className="bg-tables absolute top-0 right-0 rounded-lg px-4 py-2 text-center">
                <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
                <p className="text-[16px] font-medium text-[#050013]">18 min</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <button
              onClick={() => setIsRightPanel(true)}
              className="bg-active-status w-full rounded-full py-3 text-sm font-medium text-white transition-all hover:bg-[#00b24c]"
            >
              Start ride
            </button>
          </div>
        </div>

        {/* Right Panel */}
        <div className="relative flex w-full flex-col items-center justify-start bg-gray-100 md:w-1/2">
          {/* Map Image */}
          <img
            src="/images/map.png"
            alt="Route Map"
            className="w-full object-cover"
          />
        </div>
      </div>

      {isRightPanel && (
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out sm:hidden">
          <div className="fixed bottom-0 h-[50%] w-full rounded-tl-[15px] rounded-tr-[15px] bg-white shadow-lg sm:max-w-md">
            {/* start  */}
            <div className="relative w-full space-y-6">
              <div className="mb-0 p-6 pb-4">
                <div className="flex items-start gap-3">
                  <img
                    src="/images/avatar.jpg"
                    alt="Rider"
                    className="h-12 w-12 rounded-full object-cover"
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-[16px] font-medium text-[#050013]">
                        Rivka Frank
                      </p>
                      <div className="flex items-center text-[15px] text-yellow-500">
                        <RxStarFilled />
                        <span className="ml-1 text-[15px] font-normal text-[#050013]">
                          4.5
                        </span>
                      </div>
                    </div>
                    <div className="relative w-40">
                      <select className="appearance-none rounded-lg border-0 text-[12px] text-[#76787A] focus:outline-none">
                        <option>Fare Estimate</option>
                        <option>Premium</option>
                      </select>
                      <span className="bg-tables pointer-events-none absolute top-[14px] right-[30%] -translate-y-1/2 rounded-[12px] p-[2px]">
                        <FiChevronDown className="text-[#050013]" />
                      </span>
                    </div>

                    <div>
                      <p className="text-[20px] font-medium text-[#050013]">
                        € 35.00
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mb-0 border-t p-6 pb-0">
                <div className="relative">
                  <p className="mb-4 text-[13px] font-normal text-[#76787A]">
                    This ride has 5 Stops
                  </p>
                  <div className="space-y-0">
                    <div className="relative flex items-start gap-3">
                      <div className="flex flex-col items-center">
                        <PiMapPinLight
                          className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                          size={25}
                        />
                        <div className="h-6 border-l-2 border-dashed border-gray-300" />
                      </div>
                      <p className="text-[13px] font-normal text-[#050013]">
                        288–292, Spuistraat City
                      </p>
                    </div>

                    <div className="relative flex items-start gap-3">
                      <div className="flex flex-col items-center">
                        <LuFlagTriangleRight
                          className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                          size={25}
                        />
                      </div>
                      <p className="text-[13px] font-normal text-[#050013]">
                        1000 AP, Schipol Airport...
                      </p>
                    </div>
                  </div>

                  <div className="bg-tables absolute top-0 right-0 rounded-lg px-4 py-2 text-center">
                    <p className="text-[13px] font-normal text-[#76787A]">
                      ETA
                    </p>
                    <p className="text-[16px] font-medium text-[#050013]">
                      18 min
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <button
                  onClick={() => setIsRightPanel(true)}
                  className="bg-active-status w-full rounded-full py-3 text-sm font-medium text-white transition-all hover:bg-[#00b24c]"
                >
                  Start ride
                </button>
              </div>
            </div>
            {/* end  */}
          </div>
        </div>
      )}
    </div>
  );
}
