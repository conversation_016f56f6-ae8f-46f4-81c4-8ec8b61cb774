'use client';

import { useEffect } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { FaArrowUp } from 'react-icons/fa';
import { RxStarFilled } from 'react-icons/rx';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import Image from 'next/image';
import Link from 'next/link';

const data = [
  { month: 'Jan', income: 30 },
  { month: 'Feb', income: 100 },
  { month: 'Mar', income: 120 },
  { month: 'Apr', income: 40 },
  { month: 'May', income: 220 },
  { month: 'Jun', income: 300 },
  { month: 'Jul', income: 280 },
  { month: 'Aug', income: 240 },
  { month: 'Sep', income: 260 },
  { month: 'Oct', income: 210 },
  { month: 'Nov', income: 90 },
  { month: 'Dec', income: 320 },
];

export default function DashboardPage() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    if (setTitle) setTitle('Driver Dashboard');
  }, [setTitle]);

  return (
    <div className="space-y-6 p-6">
      {/* Main Layout Starts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* LEFT SIDE - Select + Total Earning + Trip + Bonus */}
        <div className="flex flex-col gap-4">
          {/* SELECT DROPDOWN */}
          <div className="flex justify-between sm:justify-end">
            <p className="block text-[16px] font-normal text-[#050013] sm:hidden">
              My Earning
            </p>
            <select className="text-[16px] text-[#76787A] focus:!border-0 focus:outline-none sm:text-[13px]">
              <option>Daily</option>
              <option>Weekly</option>
              <option>Monthly</option>
            </select>
          </div>

          {/* Earnings Boxes */}
          {/* <div className="flex flex-col lg:h-full lg:flex-row lg:items-stretch gap-4"> */}
          <div className="flex h-full flex-row items-stretch gap-4">
            {/* Gradient Card - Total Earning */}
            <div className="flex w-full flex-col justify-end lg:w-1/2">
              <div className="blue-gradient h-full rounded-2xl p-6 text-white">
                <div className="mb-[30%] flex items-center">
                  <p className="bg-blue rounded-full bg-[#251AA4] px-5 py-[10px] text-[30px]">
                    €
                  </p>
                </div>
                <div>
                  <p className="text-sm text-white">Total Earning</p>
                  <p className="pt-1 pb-6 text-2xl font-semibold">€ 25.00</p>
                </div>

                <div>
                  <p className="flex items-center text-sm text-green-300">
                    <span className="bg-active-status mr-3 rounded-[22px] p-[6px] text-white">
                      <FaArrowUp />
                    </span>
                    <span className="contents text-green-600">8.5%</span>
                    <span className="ml-1 contents text-white">
                      {' '}
                      Up from yesterday
                    </span>
                  </p>
                </div>
              </div>
            </div>

            {/* Trip + Bonus */}
            <div className="flex w-full flex-col gap-4 lg:w-1/2">
              {/* Trip Earning */}
              <div className="rounded-2xl border bg-white text-left">
                <div className="px-4 pt-6 text-sm text-[#76787A]">
                  Trip Earning
                </div>
                <div className="px-4 py-3 text-xl font-semibold text-[#050013]">
                  € 5.00
                </div>
                <div>
                  <p className="bg-cstm-grey flex items-center rounded-b-[15px] px-4 py-2 text-sm text-green-300">
                    <span className="bg-active-status mr-3 rounded-[22px] p-[5px] text-white">
                      <FaArrowUp />
                    </span>
                    <span className="contents text-green-600">8.5%</span>
                    <span className="ml-1 contents text-[#050013]">
                      {' '}
                      Up from yesterday
                    </span>
                  </p>
                </div>
              </div>

              {/* Bonus Box */}
              <div className="rounded-2xl border bg-white">
                <div className="px-4 pt-6 text-sm text-[#76787A]">Bonus</div>
                <div className="px-4 py-3 text-xl font-semibold text-[#050013]">
                  € 2.00
                </div>
                <div>
                  <p className="bg-cstm-grey flex items-center rounded-b-[15px] px-4 py-2 text-sm text-green-300">
                    <span className="bg-active-status mr-3 rounded-[22px] p-[5px] text-white">
                      <FaArrowUp />
                    </span>
                    <span className="contents text-green-600">8.5%</span>
                    <span className="ml-1 contents text-[#050013]">
                      {' '}
                      Up from yesterday
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* RIGHT SIDE - Graph */}
        <div className="flex h-full flex-col justify-start rounded-2xl border bg-white shadow">
          <div className="bg-cstm-grey flex items-start justify-between rounded-t-[15px] px-6 py-3">
            <h2 className="text-[16px] font-normal text-[#050013]">Income</h2>
          </div>

          <div className="min-h-[150px] flex-1 rounded-lg text-sm text-gray-400">
            <ResponsiveContainer width="100%" height={200}>
              <LineChart
                data={data}
                margin={{ top: 10, right: 20, left: 0, bottom: 30 }}
              >
                <CartesianGrid
                  strokeDasharray="4 4"
                  vertical={false}
                  stroke="#E5E7EB"
                />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ dy: 20 }}
                />
                <YAxis
                  tickFormatter={v => `€${v}`}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip formatter={v => `€${v}`} />
                <Legend
                  verticalAlign="bottom"
                  align="center"
                  content={({ payload }) => (
                    <ul>
                      {payload.map((entry, index) => (
                        <li
                          key={`item-${index}`}
                          className="hidden items-center justify-center gap-2 text-sm text-gray-600 lg:flex"
                        >
                          <span
                            className="h-2 w-2 rounded-full"
                            style={{ backgroundColor: entry.color }}
                          ></span>
                          <span>{entry.value}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                />
                <Line
                  type="monotone"
                  dataKey="income"
                  stroke="#251AA1"
                  strokeWidth={3}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Row 3: Trip History & Rating */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">
        {/* Trip History */}
        <div className="rounded-2xl border bg-white">
          <div className="mb-6 flex items-center justify-between rounded-t-2xl bg-gray-50 px-6 py-4">
            <h3 className="text-[16px] font-normal text-[#050013]">
              My Trip History
            </h3>
            <select className="text-[16px] text-[#76787A] focus:!border-0 focus:outline-none sm:text-[13px]">
              <option>Weekly</option>
              <option>Monthly</option>
            </select>
          </div>
          <div>
            <div
              className="relative mx-auto w-1/3 rounded-full border border-dashed"
              style={{ borderColor: '#D6D5D5' }}
            >
              <svg viewBox="0 0 36 36">
                <path
                  className="text-red-500"
                  strokeDasharray="100, 100"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                />
                <path
                  className="text-green-500"
                  strokeDasharray="80, 100"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center text-xl font-bold">
                <div className="text-center">
                  <p className="text-[10px] font-normal text-[#76787A] sm:text-[13px]">
                    Total Trips
                  </p>
                  <p className="text-3xl">100</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <p className="flex justify-between border-b pt-4 pb-2 text-[13px] text-[#76787A]">
                Completed Trips
                <span className="text-[18px] font-medium text-green-600">
                  80
                </span>
              </p>
              <p className="flex justify-between pt-2 text-[13px] text-[#76787A]">
                Canceled Trips
                <span className="text-[18px] font-medium text-red-500">20</span>
              </p>
            </div>
          </div>
        </div>

        {/* Rating */}
        <div className="rounded-2xl border bg-white">
          <h3 className="mb-6 flex items-center justify-between rounded-t-2xl bg-gray-50 px-6 py-4">
            My Rating & Review
          </h3>
          <div className="flex flex-col items-center py-6">
            <Image
              src="/images/avatar.jpg"
              width={64}
              height={64}
              alt="Profile"
              className="mb-2 rounded-full"
            />
            <p className="text-xl font-medium">4.5</p>
            <div className="my-3 flex text-yellow-500">
              {[...Array(4)].map((_, i) => (
                <RxStarFilled className="h-6 w-6" key={i} />
              ))}
              <RxStarFilled className="h-6 w-6 text-gray-300" />
            </div>
            <Link href="/driver/ratings">
              <button className="rounded-full border border-gray-300 px-5 py-2 text-sm hover:bg-gray-100">
                View All
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
