'use client';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { useEffect } from 'react';
import { FaStar } from 'react-icons/fa';
import { RxStarFilled } from 'react-icons/rx';

const feedbacks = [
  {
    name: '<PERSON><PERSON>',
    comment:
      'Excellent driving skills and top-notch professionalism—truly impressive!',
    rating: 4.5,
  },
  {
    name: '<PERSON>',
    comment: 'Good conversation',
    rating: 4.5,
  },
  {
    name: '<PERSON>',
    comment:
      'The ride was smooth, and the driver was very professional and courteous. Thank you for a great experience!',
    rating: 4.5,
  },
];

const tips = [
  'Keep your vehicle clean',
  'Choose optimal routes to avoid traffic',
  'Maintain professional communication',
];

export default function FeedbackCard() {
  const { setTitle } = useHeaderTitle();
  useEffect(() => {
    setTitle('Earnings');
  }, []);
  return (
    <div className="p-6 text-xl font-semibold">
      <div className="mx-auto space-y-4">
        {/* Card Section */}
        <div className="flex flex-col overflow-hidden rounded-2xl border bg-white md:flex-row">
          {/* Profile Section */}
          <div className="flex flex-col items-center justify-center border-r border-gray-100 p-6 md:w-1/3">
            <img
              src="/images/avatar.jpg"
              alt="User"
              className="mb-4 h-20 w-20 rounded-full object-cover"
            />
            <div className="flex items-center space-x-1 text-xl text-orange-400">
              {[1, 2, 3, 4].map((_, i) => (
                <RxStarFilled key={i} />
              ))}
              <RxStarFilled className="text-gray-300" />
            </div>
            <p className="mt-2 text-2xl font-normal text-[#050013]">4.5</p>
            <p className="mt-2 text-sm font-normal text-[#76787A]">
              Based on 70 rides
            </p>
          </div>

          {/* Feedback Section */}
          <div className="flex-1 bg-gray-50 p-6">
            <h3 className="mb-4 text-sm font-normal text-[#76787A]">
              Recent Feedback
            </h3>
            <div className="space-y-4">
              {feedbacks.map((item, idx) => (
                <div
                  key={idx}
                  className="flex items-start justify-between rounded-lg bg-white p-4 shadow-sm"
                >
                  <div>
                    <p className="mb-1 text-sm font-normal text-[#050013]">
                      "{item.comment}"
                    </p>
                    <p className="text-xs text-[#76787A]">{item.name}</p>
                  </div>
                  <div className="flex items-center gap-3 text-orange-400">
                    <RxStarFilled />
                    <span className="text-sm font-semibold text-black">
                      {item.rating}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tips Section */}
        <div className="rounded-2xl border bg-yellow-50">
          <h3 className="yellow-bg rounded-t-[15px] p-4 text-sm font-normal text-yellow-600">
            Tips for improvement
          </h3>
          <div className="p-4">
            <ul className="list-disc space-y-2 pl-4 text-sm font-normal text-gray-700">
              {tips.map((tip, idx) => (
                <li
                  key={idx}
                  className="text-[#050013] before:bg-gradient-to-r before:from-[#251AA1] before:to-[#2E20CB]"
                >
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
