'use client';
import { useEffect, useState, useRef } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { X } from 'lucide-react';

export default function VehicleInfoPage() {
  const { setTitle } = useHeaderTitle();
  useEffect(() => {
    setTitle('Vehicle Info');
  }, []);

  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const modalRef = useRef(null);

  useEffect(() => {
    const handleEsc = e => {
      if (e.key === 'Escape') setSelectedVehicle(null);
    };
    document.addEventListener('keydown', handleEsc);
    return () => document.removeEventListener('keydown', handleEsc);
  }, []);

  const vehicles = [
    {
      id: 1,
      type: 'Sedan',
      model: 'Hyundai Tucson',
      fuel: 'Petrol',
      number: 'NL-1234-AB',
      status: 'Approved',
      driver: '<PERSON><PERSON><PERSON>',
      inspectionStatus: 'Active',
      inspectionId: 'INSP-0001256',
      phone: '0612345678',
      date: '15-01-2024',
      image: '/images/hyundai-tucson.png',
    },
  ];

  const getStatusClasses = status => {
    switch (status) {
      case 'Pending':
        return 'text-orange-600';
      case 'Approved':
        return 'text-green-600';
      case 'Rejected':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getDotColor = status => {
    switch (status) {
      case 'Pending':
        return 'bg-orange-500';
      case 'Approved':
        return 'bg-green-600';
      case 'Rejected':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getInspectionColor = status =>
    status === 'Active' ? 'text-green-600' : 'text-gray-400';

  return (
    <div className="min-h-screen p-5">
      <div className="overflow-x-auto rounded-xl bg-white shadow">
        <table className="w-full text-left text-[14px] text-[#76787A]">
          <thead className="bg-tables border-b text-[14px] text-[#76787A]">
            <tr>
              <th className="px-6 py-4 font-medium">Type</th>
              <th className="px-6 py-4 font-medium">Model</th>
              <th className="px-6 py-4 font-medium">Fuel</th>
              <th className="px-6 py-4 font-medium">Number</th>
              <th className="px-6 py-4 font-medium">Status</th>
              <th className="px-6 py-4 font-medium">Driver</th>
              <th className="px-6 py-4 font-medium">Inspection</th>
              <th className="px-6 py-4 font-medium">Date</th>
            </tr>
          </thead>
          <tbody>
            {vehicles.map(v => (
              <tr
                key={v.id}
                onClick={() => setSelectedVehicle(v)}
                className="cursor-pointer border-b transition"
              >
                <td className="px-6 py-4 text-[#050013]">{v.type}</td>
                <td className="px-6 py-4 text-[#050013]">{v.model}</td>
                <td className="px-6 py-4 text-[#050013]">{v.fuel}</td>
                <td className="px-6 py-4 text-[#050013]">{v.number}</td>
                <td className="flex items-center gap-2 px-6 py-4 text-[#050013]">
                  <span
                    className={`h-2 w-2 rounded-full ${getDotColor(v.status)}`}
                  ></span>
                  <span
                    className={`text-xs font-medium ${getStatusClasses(v.status)}`}
                  >
                    {v.status}
                  </span>
                </td>
                <td className="px-6 py-4 text-[#050013]">{v.driver}</td>
                <td className="px-6 py-4 text-[#050013]">
                  <span
                    className={`font-normal ${getInspectionColor(v.inspectionStatus)}`}
                  >
                    {v.inspectionStatus}
                  </span>
                </td>
                <td className="px-6 py-4 text-[#050013]">{v.date}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedVehicle && (
        <div
          className="fixed inset-0 z-999 flex items-center justify-center bg-black/50 px-4 backdrop-blur-sm"
          onClick={e => {
            if (e.target === e.currentTarget) setSelectedVehicle(null);
          }}
          tabIndex={-1}
          ref={modalRef}
        >
          <div className="animate-fade-in relative flex w-full max-w-4xl flex-col overflow-hidden rounded-2xl bg-white shadow-xl md:flex-row">
            {/* Image Left */}
            <div className="h-52 w-full overflow-hidden md:h-auto md:w-1/2">
              <img
                src={selectedVehicle.image}
                alt={selectedVehicle.model}
                className="h-full w-full object-contain"
              />
            </div>

            {/* Content Right */}
            <div className="relative w-full p-6 md:w-1/2">
              {/* Close Button */}
              <button
                onClick={() => setSelectedVehicle(null)}
                className="absolute top-[-45%] right-4 text-[#76787A] transition hover:text-gray-700 sm:top-4"
              >
                <X size={24} />
              </button>

              {/* Title + Status */}
              <div className="mb-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  {selectedVehicle.model}
                </h2>
                <p className="text-sm text-gray-500">
                  {selectedVehicle.number}
                </p>
                <div className="my-2 h-[2px] w-12 bg-gray-300"></div>
                <div className="mb-2 flex items-center gap-2">
                  <span
                    className={`h-2 w-2 rounded-full ${getDotColor(selectedVehicle.status)}`}
                  ></span>
                  <span
                    className={`text-xs font-medium ${getStatusClasses(selectedVehicle.status)}`}
                  >
                    {selectedVehicle.status}
                  </span>
                </div>
              </div>

              {/* Info Table */}
              <table className="w-full border-t border-gray-100 text-left text-sm text-gray-700">
                <tbody>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">Vehicle Type</td>
                    <td className="py-2 font-medium text-[#050013]">
                      {selectedVehicle.type}
                    </td>
                  </tr>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">Fuel Type</td>
                    <td className="py-2 font-medium text-[#050013]">
                      {selectedVehicle.fuel}
                    </td>
                  </tr>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">Driver Name</td>
                    <td className="py-2 font-medium text-[#050013]">
                      {selectedVehicle.driver}
                    </td>
                  </tr>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">Phone Number</td>
                    <td className="py-2 font-medium text-[#050013]">
                      {selectedVehicle.phone}
                    </td>
                  </tr>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">
                      Inspection Status
                    </td>
                    <td
                      className={`py-2 font-medium text-[#050013] ${getInspectionColor(selectedVehicle.inspectionStatus)}`}
                    >
                      {selectedVehicle.inspectionStatus}
                    </td>
                  </tr>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">Inspection ID</td>
                    <td className="py-2 font-medium text-[#050013]">
                      {selectedVehicle.inspectionId}
                    </td>
                  </tr>
                  <tr>
                    <td className="py-2 pr-2 text-[#76787A]">
                      Registration Date
                    </td>
                    <td className="py-2 font-medium text-[#050013]">
                      {selectedVehicle.date}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
