'use client';

import React, { useEffect } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { FaArrowUp, FaPhone, FaEuroSign } from 'react-icons/fa';
import { BiPhoneCall } from 'react-icons/bi';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

const data = [
  { month: 'Jan', income: 30 },
  { month: 'Feb', income: 100 },
  { month: 'Mar', income: 120 },
  { month: 'Apr', income: 40 },
  { month: 'May', income: 220 },
  { month: 'Jun', income: 300 },
  { month: 'Jul', income: 280 },
  { month: 'Aug', income: 240 },
  { month: 'Sep', income: 260 },
  { month: 'Oct', income: 210 },
  { month: 'Nov', income: 90 },
  { month: 'Dec', income: 320 },
];

const payoutHistory = [
  {
    date: '15-01-2025',
    time: '11:27',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
  {
    date: '10-01-2025',
    time: '12:00',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
  {
    date: '09-01-2025',
    time: '10:25',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
  {
    date: '08-01-2025',
    time: '11:27',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
  {
    date: '07-01-2025',
    time: '10:25',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
  {
    date: '06-01-2025',
    time: '11:27',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
  {
    date: '04-01-2025',
    time: '11:27',
    tripId: '€22.00',
    amount: '98%',
    charges: 'Fee - € 5.00',
  },
];

export default function EarningsDashboard() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle('Earnings Dashboard');
  }, []);

  return (
    <div className="relative min-h-screen bg-white p-4 font-sans text-gray-800 md:p-6">
      {/* Main Layout Starts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* LEFT SIDE - Select + Total Earning + Trip + Bonus */}
        <div className="flex flex-col gap-4">
          {/* SELECT DROPDOWN */}
          <div className="flex justify-between sm:justify-end">
            <p className="block text-[16px] font-normal text-[#050013] sm:hidden">
              My Earning
            </p>
            <select className="text-[16px] text-[#76787A] focus:!border-0 focus:outline-none sm:text-[13px]">
              <option>Daily</option>
              <option>Weekly</option>
              <option>Monthly</option>
            </select>
          </div>

          {/* Earnings Box Wrapper */}
          <div className="flex h-full flex-col gap-4 lg:flex-row lg:items-stretch">
            {/* Total Earning */}
            <div className="w-full lg:w-1/2">
              <div className="blue-gradient align-center flex h-full justify-between rounded-2xl p-6 text-white sm:block">
                <div className="mb-0 flex items-center lg:mb-[28%]">
                  <p className="rounded-full bg-[#251AA4] px-5 py-[10px] text-[30px]">
                    €
                  </p>
                </div>
                <div>
                  <p className="text-sm text-white">Total Earning</p>
                  <p className="pt-1 text-2xl font-semibold sm:pb-6">€ 25.00</p>
                </div>
                <div className="hidden sm:block">
                  <p className="flex items-center text-sm text-green-300">
                    <span className="bg-active-status mr-3 rounded-[22px] p-[6px] text-white">
                      <FaArrowUp />
                    </span>
                    <span className="contents text-green-600">8.5%</span>
                    <span className="ml-1 contents text-white">
                      {' '}
                      Up from yesterday
                    </span>
                  </p>
                </div>
              </div>
            </div>

            {/* Trip + Bonus Wrapper */}
            <div className="flex w-full flex-row gap-4 lg:w-1/2 lg:flex-col">
              {/* Trip Earning */}
              <div className="w-1/2 rounded-2xl border bg-white text-left lg:w-full">
                <div className="px-4 pt-6 text-sm text-[#76787A]">
                  Trip Earning
                </div>
                <div className="px-4 py-3 text-xl font-semibold text-[#050013]">
                  € 5.00
                </div>
                <div>
                  <p className="bg-cstm-grey flex items-center rounded-b-[15px] px-4 py-2 text-sm text-green-300">
                    <span className="bg-active-status mr-3 rounded-[22px] p-[5px] text-white">
                      <FaArrowUp />
                    </span>
                    <span className="contents text-green-600">8.5%</span>
                    <span className="ml-1 contents text-[#050013]">
                      {' '}
                      Up from yesterday
                    </span>
                  </p>
                </div>
              </div>

              {/* Bonus Box */}
              <div className="w-1/2 rounded-2xl border bg-white text-left lg:w-full">
                <div className="px-4 pt-6 text-sm text-[#76787A]">Bonus</div>
                <div className="px-4 py-3 text-xl font-semibold text-[#050013]">
                  € 2.00
                </div>
                <div>
                  <p className="bg-cstm-grey flex items-center rounded-b-[15px] px-4 py-2 text-sm text-green-300">
                    <span className="bg-active-status mr-3 rounded-[22px] p-[5px] text-white">
                      <FaArrowUp />
                    </span>
                    <span className="contents text-green-600">8.5%</span>
                    <span className="ml-1 contents text-[#050013]">
                      {' '}
                      Up from yesterday
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* RIGHT SIDE - Graph */}
        <div className="flex h-full flex-col justify-start rounded-2xl border bg-white shadow">
          <div className="bg-cstm-grey flex items-start justify-between rounded-t-[15px] px-6 py-3">
            <h2 className="text-[16px] font-normal text-[#050013]">
              Incentive Progress
            </h2>
            <select className="px-3 py-1 text-sm text-[#76787A]">
              <option>Week</option>
              <option>Month</option>
            </select>
          </div>
          <div className="text[#050013] m-4 rounded-lg bg-[#FFF7EB] px-4 py-3 text-center text-[13px] font-normal">
            🥳 Complete 12 more rides to earn $150 bonus
          </div>
          <div className="graph-earning min-h-[150px] flex-1 rounded-lg text-sm text-gray-400">
            <ResponsiveContainer width="100%" height={200}>
              <LineChart
                data={data}
                margin={{ top: 10, right: 20, left: 0, bottom: 30 }}
              >
                <CartesianGrid
                  strokeDasharray="4 4"
                  vertical={false}
                  stroke="#E5E7EB"
                />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ dy: 20 }}
                />
                <YAxis
                  tickFormatter={v => `€${v}`}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip formatter={v => `€${v}`} />
                {/* <Legend /> */}
                <Legend
                  verticalAlign="bottom"
                  align="center"
                  content={({ payload }) => (
                    <ul>
                      {payload.map((entry, index) => (
                        <li
                          key={`item-${index}`}
                          className="hidden items-center justify-center gap-2 text-sm text-gray-600 lg:flex"
                        >
                          {/* Replace icon with dot */}
                          <span
                            className="h-2 w-2 rounded-full"
                            style={{ backgroundColor: entry.color }}
                          ></span>
                          <span>{entry.value}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                />
                <Line
                  type="monotone"
                  dataKey="income"
                  stroke="#251AA1"
                  strokeWidth={3}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Payout History Section */}
      <div className="mt-10 overflow-x-auto rounded-2xl bg-white shadow">
        <div className="bg-tables border-b p-4 text-lg font-normal">
          Payout History
        </div>
        <table className="min-w-full divide-y">
          <thead className="text-sm font-normal text-[#76787A]">
            <tr>
              <th className="px-6 py-3 text-left font-medium">Date</th>
              <th className="px-6 py-3 text-left font-medium">Time</th>
              <th className="px-6 py-3 text-left font-medium">Trip ID</th>
              <th className="px-6 py-3 text-left font-medium">Amount</th>
              <th className="px-6 py-3 text-left font-medium">Other charges</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white text-sm text-[#050013]">
            {payoutHistory.map((entry, idx) => (
              <tr key={idx}>
                <td className="px-6 py-4 whitespace-nowrap">{entry.date}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.time}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.tripId}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.amount}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.charges}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Sticky Call Icon */}
      <div className="fixed right-6 bottom-6 z-50">
        <button className="flex h-12 w-12 items-center justify-center rounded-full bg-black text-white shadow-lg hover:bg-gray-800">
          <BiPhoneCall className="h-[25px] text-xl" />
        </button>
      </div>
    </div>
  );
}
