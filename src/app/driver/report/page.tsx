'use client';

import { useState, useEffect } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { FiAlertTriangle, FiMic, FiCamera, FiX } from 'react-icons/fi';
import { FaEuroSign, FaUser } from 'react-icons/fa';
import { HiOutlineCurrencyEuro } from 'react-icons/hi';
import { BiNavigation } from 'react-icons/bi';
import { IoIosArrowForward } from 'react-icons/io';
import { TbReceipt, TbUsers } from 'react-icons/tb';

const issues = [
  {
    label: 'Safety Concern',
    icon: <FiAlertTriangle className="text-xl text-red-400" />,
    color: 'bg-red-100',
  },
  {
    label: 'Fare Dispute',
    icon: <TbReceipt className="text-xl text-orange-400" />,
    color: 'bg-orange-100',
  },
  {
    label: 'Payment issue',
    icon: <HiOutlineCurrencyEuro className="text-xl text-blue-500" />,
    color: 'bg-purple-100',
  },
  {
    label: 'Passenger Behaviour',
    icon: <TbUsers className="text-xl text-gray-600" />,
    color: 'bg-gray-100',
  },
  {
    label: 'Navigation issue',
    icon: <BiNavigation className="text-xl text-rose-400" />,
    color: 'bg-rose-100',
  },
];

export default function ReportPage() {
  const { setTitle } = useHeaderTitle();
  const [selected, setSelected] = useState(null);

  useEffect(() => {
    setTitle('Report');
  }, [setTitle]);

  return (
    <div className="bg-tables relative w-full rounded-2xl border p-6">
      <h2 className="mb-6 text-lg font-normal text-[#050013]">
        Report an issue
      </h2>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        {issues.slice(0, 3).map((item, idx) => (
          <button
            key={idx}
            onClick={() => setSelected(item)}
            className="flex items-center justify-between rounded-xl bg-white p-4 shadow-sm transition hover:shadow-md"
          >
            <div className="flex items-center gap-3">
              <div
                className={`h-10 w-10 ${item.color} flex items-center justify-center rounded-full`}
              >
                {item.icon}
              </div>
              <span className="text-left text-sm font-medium text-[#050013]">
                {item.label}
              </span>
            </div>
            <IoIosArrowForward className="text-gray-400" />
          </button>
        ))}
      </div>

      <p className="mt-6 mb-2 text-sm text-[#76787A]">Other issues</p>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        {issues.slice(3).map((item, idx) => (
          <button
            key={idx}
            onClick={() => setSelected(item)}
            className="flex items-center justify-between rounded-xl bg-white p-4 shadow-sm transition hover:shadow-md"
          >
            <div className="flex items-center gap-3">
              <div
                className={`h-10 w-10 ${item.color} flex items-center justify-center rounded-full`}
              >
                {item.icon}
              </div>
              <span className="text-left text-sm font-medium text-[#050013]">
                {item.label}
              </span>
            </div>
            <IoIosArrowForward className="text-gray-400" />
          </button>
        ))}
      </div>

      {/* Slide-in Modal */}
      {selected && (
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
          <div className="r absolute bottom-0 flex h-[600px] w-full translate-x-0 transform flex-col justify-between overflow-y-scroll rounded-tl-[18px] rounded-tr-[18px] bg-white shadow-lg transition-all duration-500 sm:relative sm:h-full sm:max-w-md sm:translate-x-0 sm:overflow-visible sm:rounded-tr-none">
            <div>
              <div className="bg-cstm-grey mb-6 flex items-center justify-between p-6">
                <h3 className="text-lg font-normal text-[#050013]">
                  {selected.label}
                </h3>
                <button
                  onClick={() => setSelected(null)}
                  className="text-xl text-gray-400 hover:text-black"
                >
                  <FiX />
                </button>
              </div>

              <div className="space-y-4 p-6">
                <textarea
                  placeholder="Describe your issue"
                  className="w-full rounded border border-gray-200 p-3 text-sm placeholder:text-gray-400 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                  rows={3}
                ></textarea>

                <div className="flex items-center justify-between rounded border border-gray-200 px-3 py-5">
                  <input
                    type="text"
                    placeholder="Record issue"
                    className="flex-1 bg-transparent text-sm outline-none placeholder:text-gray-400"
                  />
                  <span className="bg-cstm-grey rounded-[20px] p-2">
                    {' '}
                    <FiMic className="text-lg text-blue-500" />
                  </span>
                </div>
                <div className="rounded border p-2">
                  <div className="flex flex-col items-center justify-center rounded border border-dashed border-gray-300 p-6 text-gray-400">
                    <FiCamera className="mb-2 text-2xl" />
                    <span className="text-sm">Add photo</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end border-t p-6">
              <button className="w-full rounded-full bg-[#3707EF] px-6 py-3 text-[13px] font-semibold text-white hover:bg-[#3e1ed0] sm:w-auto">
                Submit Report
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
