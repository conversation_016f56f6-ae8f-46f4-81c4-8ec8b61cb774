'use client';

import { useEffect, useState } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { LuPencil } from 'react-icons/lu';
import { BsCheckCircleFill } from 'react-icons/bs';
import { Cloud } from '@/icons';
import { FaFileAlt, FaFilePdf, FaFileWord } from 'react-icons/fa';
import { BiError } from 'react-icons/bi';
import { RiVipDiamondLine } from 'react-icons/ri';
import { FiX } from 'react-icons/fi';
import { HiOutlineCreditCard } from 'react-icons/hi';
import { GoChecklist, GoCheckCircleFill } from 'react-icons/go';

export default function DriverProfilePage() {
  const { setTitle } = useHeaderTitle();
  const [isRightPanel, setIsRightPanel] = useState<boolean>(false);

  useEffect(() => {
    setTitle('Driver Profile');
  }, []);

  return (
    <div className="mx-auto max-w-7xl p-4">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Left: Profile Card */}
        <div className="relative rounded-xl border bg-white">
          <div
            onClick={() => setIsRightPanel(true)}
            className="bg-tables mb-4 flex items-center justify-between rounded-xl rounded-b-none px-6 py-3"
          >
            <h2 className="font-semibold text-gray-900">My Profile</h2>
            <LuPencil className="cursor-pointer text-gray-500" />
          </div>

          {/* Profile Info */}
          <div className="flex items-center gap-4 px-6 py-3">
            <img
              src="/images/avatar.jpg"
              alt="Rivka Frank"
              className="h-16 w-16 rounded-full object-cover"
            />
            <div>
              <p className="text-[18px] font-medium text-[#050013]">
                Rivka Frank
              </p>
              <p className="text-[14px] font-medium text-[#76787A]">
                ID: <span className="text-purple">DRV-20250123</span>
              </p>
              <p className="mt-1 text-[14px] text-[#13BB76]">● Active</p>
            </div>
          </div>

          {/* Warning */}
          <div className="p-6">
            <div className="flex items-center justify-center gap-3 rounded-lg border border-red-100 bg-red-50 p-3 text-[12px] font-normal text-[#FF2626]">
              <BiError size={20} /> Your driver’s license expires in 30 days
            </div>
          </div>
        </div>

        {/* Right: Document Section */}
        <div>
          <h2 className="mb-4 text-[13px] font-normal text-[#76787A]">
            Document
          </h2>
          <div className="space-y-4">
            {/* Document 1 */}
            <div className="flex items-center justify-between rounded-xl border bg-white p-4">
              <div className="flex items-center gap-4">
                <span className="rounded-full bg-indigo-50 p-[6px]">
                  <HiOutlineCreditCard className="text-xl text-indigo-600" />
                </span>
                <div>
                  <p className="text-[13px] font-medium text-[#050013]">
                    Driver's License
                  </p>
                  <p className="text-[13px] text-[#76787A]">
                    Expires: Mar 15, 2025
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-sm font-medium text-[#00CA77]">
                <GoCheckCircleFill size={20} className="text-[#00CA77]" />
                Verified
              </div>
            </div>

            {/* Document 2 */}
            <div className="flex items-center justify-between rounded-xl border bg-white p-4">
              <div className="flex items-center gap-4">
                <span className="rounded-full bg-indigo-50 p-[6px]">
                  <RiVipDiamondLine className="text-xl text-indigo-600" />
                </span>
                <div>
                  <p className="text-[13px] font-medium text-[#050013]">
                    Vehicle_Insurance.pdf
                  </p>
                  <p className="text-[13px] text-[#76787A]">
                    Expires: Jun 15, 2027
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-sm font-medium text-[#00CA77]">
                <GoCheckCircleFill size={20} className="text-[#00CA77]" />
                Verified
              </div>
            </div>

            {/* Document 3 */}
            <div className="flex items-center justify-between rounded-xl border bg-white p-4">
              <div className="flex items-center gap-4">
                <span className="rounded-full bg-indigo-50 p-[6px]">
                  <GoChecklist className="text-xl text-indigo-600" />
                </span>
                <div>
                  <p className="text-[13px] font-medium text-[#050013]">
                    Background_Check.docx
                  </p>
                  <p className="text-[13px] text-[#76787A]">
                    Expires: April 15, 2026
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-sm font-medium text-[#00CA77]">
                <GoCheckCircleFill size={20} className="text-[#00CA77]" />
                Verified
              </div>
            </div>
          </div>
        </div>
      </div>

      {isRightPanel && (
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
          <div className="r absolute bottom-0 flex h-[600px] w-full translate-x-0 transform flex-col justify-between overflow-y-scroll rounded-tl-[18px] rounded-tr-[18px] bg-white shadow-lg transition-all duration-500 sm:relative sm:h-full sm:max-w-md sm:translate-x-0 sm:overflow-visible sm:rounded-tr-none">
            {/* Header */}
            <div>
              <div className="bg-tables mb-6 flex items-center justify-between p-6">
                <div>
                  <h3 className="text-[20px] font-normal text-[#050013]">
                    Edit Profile
                  </h3>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setIsRightPanel(false)}
                    className="text-xl text-gray-400 hover:text-black"
                  >
                    <FiX />
                  </button>
                </div>
              </div>

              {/* Form Fields */}
              <div className="space-y-6 px-6">
                {/* Full Name */}
                <div className="relative w-full rounded-lg border border-gray-300 px-4 pt-3 pb-2">
                  <p className="absolute top-[-10px] mb-1 bg-white text-[13px] text-[#76787A]">
                    Full Name
                  </p>
                  <input
                    type="text"
                    defaultValue="Rivka Frank"
                    className="w-full text-[13px] text-[#050013] placeholder-[#050013] focus:outline-none"
                    placeholder="Enter full name"
                  />
                </div>

                {/* ID */}
                <div className="relative w-full rounded-lg border border-gray-300 px-4 pt-3 pb-2">
                  <p className="absolute top-[-10px] mb-1 bg-white text-[13px] text-[#76787A]">
                    ID
                  </p>
                  <input
                    type="text"
                    defaultValue="DRV-20250123"
                    className="w-full text-[13px] text-[#050013] placeholder-[#050013] focus:outline-none"
                    placeholder="Enter ID"
                  />
                </div>

                {/* Upload Document */}
                <div className="mt-3 mb-1 rounded-lg border border-gray-200 bg-white p-4 shadow-none dark:border-gray-700 dark:bg-gray-800">
                  <div className="flex w-full items-center justify-center">
                    <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-8">
                      <div className="flex items-center justify-center pt-5 pb-6">
                        <Cloud />
                        <p className="text-dark-grey dark:text-gray-400">
                          <span className="px-3 text-[13px]">
                            Click or drag file to this area to upload
                          </span>
                        </p>
                      </div>
                      <input
                        id="dropzone-file"
                        type="file"
                        className="hidden"
                      />
                    </label>
                  </div>
                </div>
                <p className="text-[13px] text-[#76787A]">
                  Formats accepted are PNG & JPG
                </p>
              </div>
            </div>

            {/* Bottom CTA */}
            <div className="flex justify-end border-t p-6">
              <button className="w-full rounded-full bg-[#3707EF] px-6 py-3 text-[13px] font-semibold text-white hover:bg-[#3e1ed0] sm:w-auto">
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
