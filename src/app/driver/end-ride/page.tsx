'use client';

import { useEffect, useState } from 'react';
import { useHeaderTitle } from '@/context/HeaderTitleContext';
import { TbPhoneCall } from 'react-icons/tb';
import { FiChevronDown, FiX } from 'react-icons/fi';
import { PiMapPinLight } from 'react-icons/pi';
import { RxStarFilled } from 'react-icons/rx';
import { LuFlagTriangleRight } from 'react-icons/lu';
import { GoDownload } from 'react-icons/go';

export default function StartRidePage() {
  const { setTitle } = useHeaderTitle();
  const [isRightPanel, setIsRightPanel] = useState<boolean>(false);
  const [isRoutePanel, setIsRoutePanel] = useState<boolean>(false);
  useEffect(() => {
    setTitle('Start Ride');
  }, []);

  return (
    <div className="mx-auto max-w-7xl sm:p-4">
      <div className="flex flex-col overflow-hidden rounded-2xl border bg-white md:flex-row">
        {/* Left Panel */}
        <div className="relative hidden w-full space-y-6 sm:block md:w-1/2">
          {/* Rider Info */}
          <div className="space-y-4 p-6">
            {/* Name and Rating */}
            <div className="flex items-start gap-3">
              <img
                src="/images/avatar.jpg"
                alt="Rider"
                className="h-12 w-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-2">
                  <p className="text-[16px] font-medium text-[#050013]">
                    Rivka Frank
                  </p>
                  <div className="flex items-center text-[15px] text-yellow-500">
                    <RxStarFilled />
                    <span className="ml-1 text-[15px] font-normal text-[#050013]">
                      4.5
                    </span>
                  </div>
                </div>

                <div className="relative w-40">
                  <select className="appearance-none rounded-lg border-0 text-[12px] text-[#76787A] focus:outline-none">
                    <option>Fare Estimate</option>
                    <option>Premium</option>
                  </select>
                  <span className="bg-tables pointer-events-none absolute top-[14px] right-[30%] -translate-y-1/2 rounded-[12px] p-[2px]">
                    <FiChevronDown className="text-[#050013]" />
                  </span>
                </div>

                <div>
                  <p className="text-[20px] font-medium text-[#050013]">
                    € 35.00
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Stops */}
          <div className="border-t p-6">
            <div className="relative">
              <p className="mb-4 text-[13px] font-normal text-[#76787A]">
                This ride has 5 Stops
              </p>
              <div className="space-y-0">
                {/* Stop 1 */}
                <div className="relative flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <PiMapPinLight
                      className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                      size={25}
                    />
                    <div className="h-6 border-l-2 border-dashed border-gray-300" />
                  </div>
                  <p className="text-[13px] font-normal text-[#050013]">
                    288–292, Spuistraat City
                  </p>
                </div>
                {/* Stop 2 */}
                <div className="relative flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <LuFlagTriangleRight
                      className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                      size={25}
                    />
                  </div>
                  <p className="text-[13px] font-normal text-[#050013]">
                    1000 AP, Schipol Airport...
                  </p>
                </div>
              </div>
              {/* ETA Box inside Stops RHS */}
              <div className="bg-tables absolute top-0 right-0 rounded-lg px-4 py-2 text-center">
                <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
                <p className="text-[16px] font-medium text-[#050013]">18 min</p>
              </div>
            </div>
          </div>
          {/* Start Ride CTA */}
          <div className="p-6">
            <button
              onClick={() => setIsRightPanel(true)}
              className="bg-active-status w-full rounded-full py-3 text-sm font-medium text-white transition-all hover:bg-[#00b24c]"
              style={{ backgroundColor: '#FF5F5F' }}
            >
              End route
            </button>
          </div>
        </div>

        {/* Right Panel */}
        <div className="relative flex w-full flex-col items-center justify-start bg-gray-100 md:w-1/2">
          {/* Map Image */}
          <img
            src="/images/map.png"
            alt="Route Map"
            className="w-full object-cover"
          />
        </div>
      </div>
      {/* Floating Phone Icon Button */}
      <div className="fixed right-6 bottom-6 z-50">
        <button className="rounded-full bg-black p-4 text-white shadow-lg hover:bg-gray-800">
          <TbPhoneCall className="h-5 w-5" />
        </button>
      </div>

      {isRightPanel && (
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
          <div className="flex h-full w-full translate-x-0 transform flex-col justify-between rounded-l-2xl bg-white shadow-lg transition-all duration-500 sm:max-w-md sm:translate-x-0">
            {/* Header */}
            <div>
              <div className="bg-tables mb-6 flex items-center justify-between rounded-tl-[18px] p-6">
                <div>
                  <h3 className="text-[20px] font-normal text-[#050013]">
                    Ride Fare Details
                  </h3>
                  <p className="mt-1 text-[12px] text-[#76787A]">
                    Today at 2:45 PM
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <button className="text-purple border-btn rounded-full border p-2">
                    <GoDownload className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setIsRightPanel(false)}
                    className="text-xl text-gray-400 hover:text-black"
                  >
                    <FiX />
                  </button>
                </div>
              </div>

              {/* Route & Fare Box */}
              <div className="mx-6 mb-6 rounded bg-[#F3F5F7] p-4">
                <div className="space-y-4">
                  {/* Start Point */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="mb-0 flex flex-col items-center">
                        <PiMapPinLight
                          className="text-purple rounded-full bg-white p-[4px]"
                          size={24}
                        />
                        {/* Dashed Line */}
                        <div className="h-6 border-l-2 border-dashed border-gray-300" />
                      </div>
                      <p className="text-[13px] font-normal text-[#050013]">
                        288–292, Spuistraat City
                      </p>
                    </div>
                    <p className="text-[18px] font-medium text-[#050013]">
                      € 35.00
                    </p>
                  </div>

                  {/* End Point */}
                  <div className="flex items-start gap-3">
                    <div className="flex flex-col items-center">
                      <LuFlagTriangleRight
                        className="text-purple rounded-full bg-white p-[4px]"
                        size={24}
                      />
                    </div>
                    <p className="text-[13px] font-normal text-[#050013]">
                      1000 AP, Schipol Airport...
                    </p>
                  </div>
                </div>
              </div>

              {/* Details */}
              <div className="space-y-6 px-6">
                <div className="space-y-3 text-sm text-[#050013]">
                  <div className="flex justify-between text-[14px] text-[#76787A]">
                    <span>Total distance traveled</span>
                    <span className="text-[#050013]">30 KM</span>
                  </div>
                  <div className="flex justify-between text-[#76787A]">
                    <span>Total time spent</span>
                    <span className="text-[#050013]">1:00 hr</span>
                  </div>
                  <div className="flex justify-between text-[#76787A]">
                    <span>Payment</span>
                    <span className="text-[#050013]">By card</span>
                  </div>
                </div>

                <div>
                  <p className="mb-4 text-[16px] font-medium text-[#050013]">
                    Fare Breakdown
                  </p>
                  <div className="space-y-3 text-sm text-[14px] text-[#050013]">
                    <div className="flex justify-between text-[#76787A]">
                      <span>Base Fare</span>
                      <span className="text-[#050013]">€ 25.00</span>
                    </div>
                    <div className="flex justify-between text-[#76787A]">
                      <span>Wait fees</span>
                      <span className="text-[#050013]">€ 5.00</span>
                    </div>
                    <div className="flex justify-between text-[#76787A]">
                      <span>Distance (1 KM)</span>
                      <span className="text-[#050013]">€ 1.00</span>
                    </div>
                    <div className="flex justify-between text-[#76787A]">
                      <span>Service fee</span>
                      <span className="text-[#050013]">€ 4.00</span>
                    </div>
                    <hr />
                    <div className="flex justify-between text-[#76787A]">
                      <span className="text-[13px]">Total Earning</span>
                      <span className="text-[16px] font-semibold text-[#050013]">
                        € 35.00
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom CTA */}
            <div className="flex justify-end border-t p-6">
              <button className="border-btn text-purple rounded-full border px-4 py-3 text-[13px] font-semibold hover:bg-[#f2efff]">
                Report an issue
              </button>
            </div>
          </div>
        </div>
      )}
      {/* end route modal start  */}

      <div className="fixed inset-0 flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out sm:hidden">
        <div className="fixed bottom-0 h-[50%] w-full rounded-tl-[15px] rounded-tr-[15px] bg-white shadow-lg sm:max-w-md">
          <div className="relative w-full space-y-6 md:w-1/2">
            {/* Rider Info */}
            <div className="mb-0 space-y-4 p-6 pb-4">
              {/* Name and Rating */}
              <div className="flex items-start gap-3">
                <img
                  src="/images/avatar.jpg"
                  alt="Rider"
                  className="h-12 w-12 rounded-full object-cover"
                />
                <div>
                  <div className="flex items-center gap-2">
                    <p className="text-[16px] font-medium text-[#050013]">
                      Rivka Frank
                    </p>
                    <div className="flex items-center text-[15px] text-yellow-500">
                      <RxStarFilled />
                      <span className="ml-1 text-[15px] font-normal text-[#050013]">
                        4.5
                      </span>
                    </div>
                  </div>

                  <div className="relative w-40">
                    <select className="appearance-none rounded-lg border-0 text-[12px] text-[#76787A] focus:outline-none">
                      <option>Fare Estimate</option>
                      <option>Premium</option>
                    </select>
                    <span className="bg-tables pointer-events-none absolute top-[14px] right-[30%] -translate-y-1/2 rounded-[12px] p-[2px]">
                      <FiChevronDown className="text-[#050013]" />
                    </span>
                  </div>

                  <div>
                    <p className="text-[20px] font-medium text-[#050013]">
                      € 35.00
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Stops */}
            <div className="mb-0 border-t p-6 pb-0">
              <div className="relative">
                <p className="mb-4 text-[13px] font-normal text-[#76787A]">
                  This ride has 5 Stops
                </p>
                <div className="space-y-0">
                  {/* Stop 1 */}
                  <div className="relative flex items-start gap-3">
                    <div className="flex flex-col items-center">
                      <PiMapPinLight
                        className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                        size={25}
                      />
                      <div className="h-6 border-l-2 border-dashed border-gray-300" />
                    </div>
                    <p className="text-[13px] font-normal text-[#050013]">
                      288–292, Spuistraat City
                    </p>
                  </div>
                  {/* Stop 2 */}
                  <div className="relative flex items-start gap-3">
                    <div className="flex flex-col items-center">
                      <LuFlagTriangleRight
                        className="z-10 rounded-full bg-blue-50 p-[4px] text-[#6F3FF5]"
                        size={25}
                      />
                    </div>
                    <p className="text-[13px] font-normal text-[#050013]">
                      1000 AP, Schipol Airport...
                    </p>
                  </div>
                </div>
                {/* ETA Box inside Stops RHS */}
                <div className="bg-tables absolute top-0 right-0 rounded-lg px-4 py-2 text-center">
                  <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
                  <p className="text-[16px] font-medium text-[#050013]">
                    18 min
                  </p>
                </div>
              </div>
            </div>
            {/* Start Ride CTA */}
            <div className="p-6">
              <button
                onClick={() => setIsRightPanel(true)}
                className="bg-active-status z-999 w-full rounded-full py-3 text-sm font-medium text-white transition-all hover:bg-[#00b24c]"
                style={{ backgroundColor: '#FF5F5F' }}
              >
                End route
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* end route modal end  */}
    </div>
  );
}
