'use client';
import React, { useState } from 'react';
import {
  FiVolume2,
  FiAlertTriangle,
  FiFileText,
  FiMapPin,
  FiCreditCard,
  FiX,
} from 'react-icons/fi';

const reports = [
  {
    id: 1,
    type: 'Safety Concern',
    description: 'Passenger misbehaved and violated seatbelt rules.',
    audioUrl: '/audio/sample1.mp3',
    imageUrl: '/images/seatbelt.jpg',
  },
  {
    id: 2,
    type: 'Fare Dispute',
    description: 'Charged more than estimated fare.',
    audioUrl: '/audio/sample2.mp3',
    imageUrl: '/images/open-door.jpg',
  },
  {
    id: 3,
    type: 'Navigation Issue',
    description: 'Driver was guided to incorrect pickup point.',
    audioUrl: '/audio/sample3.mp3',
    imageUrl: '/images/pickup.jpg',
  },
  {
    id: 4,
    type: 'Payment Issue',
    description: 'UPI payment failed after ride completion.',
    audioUrl: '/audio/sample4.mp3',
    imageUrl: '/images/pickup.jpg',
  },
];

const getIcon = type => {
  switch (type) {
    case 'Safety Concern':
      return <FiAlertTriangle className="text-xl text-red-500" />;
    case 'Fare Dispute':
      return <FiFileText className="text-xl text-yellow-500" />;
    case 'Navigation Issue':
      return <FiMapPin className="text-xl text-pink-500" />;
    case 'Payment Issue':
      return <FiCreditCard className="text-xl text-purple-500" />;
    default:
      return <FiFileText className="text-xl text-gray-400" />;
  }
};

const ReportListingPage = () => {
  const [modalImage, setModalImage] = useState(null);

  return (
    <div className="mx-auto max-w-6xl px-4">
      <h1 className="mb-10 text-[18px] font-bold text-gray-800">
        Reported Issues
      </h1>

      <div className="space-y-6">
        {reports.map(report => (
          <div
            key={report.id}
            className="flex flex-col justify-between gap-4 rounded-2xl border border-gray-100 bg-white p-6 shadow-md transition hover:shadow-lg sm:flex-row sm:items-center"
          >
            {/* Left: Icon + Info */}
            <div className="flex flex-1 items-start gap-4">
              <div className="rounded-full bg-gray-100 p-3 shadow-sm">
                {getIcon(report.type)}
              </div>

              <div className="space-y-2">
                <p className="text-[15px] leading-relaxed text-gray-800">
                  {report.description}
                </p>

                <button
                  onClick={() => new Audio(report.audioUrl).play()}
                  className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-600 transition hover:bg-blue-100"
                >
                  <FiVolume2 className="text-base" />
                  Play Audio
                </button>
              </div>
            </div>

            {/* Right: Image Preview */}
            <div
              onClick={() => setModalImage(report.imageUrl)}
              className="h-28 w-full cursor-pointer overflow-hidden rounded-xl border transition hover:scale-[1.03] sm:w-40"
            >
              <img
                src={report.imageUrl}
                alt="Issue"
                className="h-full w-full object-cover"
                onError={e =>
                  (e.target.src =
                    'https://via.placeholder.com/300x200?text=No+Image')
                }
              />
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {modalImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
          onClick={() => setModalImage(null)}
        >
          <div className="relative w-full max-w-3xl px-4">
            <button
              className="absolute top-4 right-4 text-2xl text-white"
              onClick={() => setModalImage(null)}
            >
              <FiX />
            </button>
            <img
              src={modalImage}
              alt="Full view"
              className="max-h-[80vh] w-full rounded-xl border object-contain shadow-2xl"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportListingPage;
