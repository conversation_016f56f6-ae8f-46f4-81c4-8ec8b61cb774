'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

import Input from '@/components/form/input/InputField';
import Label from '@/components/form/Label';
import Button from '@/components/ui/button/Button';
import { EyeCloseIcon, EyeIcon } from '@/icons';
import { superAdminSignInQuery } from '@/state/querie';

export default function SuperAdmin() {
  const router = useRouter();

  // State management
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  /**
   * Validates the login form fields
   */
  const validateForm = () => {
    const trimmedEmail = email.trim();
    const trimmedPassword = password.trim();

    if (!trimmedEmail || !trimmedPassword) {
      setError('Username and password are required.');
      return false;
    }
    if (/\s/.test(trimmedEmail)) {
      setError('Username cannot contain spaces.');
      return false;
    }
    if (trimmedPassword.length < 6) {
      setError('Password must be at least 6 characters.');
      return false;
    }

    setError('');
    return true;
  };

  /**
   * Handles login form submission
   */
  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      const payload = { email, password };
      const response = await superAdminSignInQuery(payload);
      if (response?.success) {
        localStorage.setItem('token', response.data?.token);
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem(
          'account',
          JSON.stringify(response.data?.superAdmin || response.data)
        );
        if (response.data?.role === 'client') {
          localStorage.setItem('role', response?.data?.role);
          router.replace('/admin/dashboard');
          return;
        }
        localStorage.setItem('role', response.data?.superAdmin?.role);

        router.replace('/');
      } else {
        setError('Login failed. Please check your username and password.');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Login failed. Please check your username and password.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative z-1 bg-white p-6 sm:p-0">
      <div className="relative flex h-screen w-full flex-col justify-center sm:p-0 lg:flex-row">
        {/* Left Section - Image */}
        <div className="relative hidden h-full w-full items-center lg:grid lg:w-1/2 dark:bg-white/5">
          <Image
            className="h-screen w-full"
            width={100}
            height={100}
            src="/images/bg-image/login_bg.png"
            alt="background-image"
          />
          <Image
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            width={219}
            height={48}
            src="/images/logo/auth-logo.png"
            alt="logo-image"
          />
        </div>

        {/* Right Section - Form */}
        <div className="flex w-full flex-1 flex-col bg-white lg:w-1/2">
          <div className="mx-auto flex w-full max-w-md flex-1 flex-col justify-center">
            {/* Title */}
            <div className="mb-5 sm:mb-8">
              <h1 className="font-poppins mb-2 text-[1.875rem] font-semibold text-gray-800">
                Hey,
                <br /> Welcome Back!
              </h1>
              <p className="font-poppins text-sm text-gray-500">
                We are very happy to see you back!
              </p>
            </div>

            {/* Login Form */}
            <form onSubmit={handleLogin}>
              <div className="space-y-6">
                {/* Email Field */}
                <div>
                  <Label className="text-[14px] font-semibold text-[#595959]">
                    Email
                  </Label>
                  <Input
                    placeholder="Enter email"
                    type="text"
                    onChange={e => setEmail(e.target.value)}
                    value={email}
                    className="rounded-[5px] border border-[#e1e1e1] bg-white p-[10.5px_14px] text-black"
                  />
                </div>

                {/* Password Field */}
                <div>
                  <Label className="text-[14px] font-semibold text-[#595959]">
                    Password
                  </Label>
                  <div className="relative">
                    <Input
                      placeholder="Enter password"
                      type={showPassword ? 'text' : 'password'}
                      onChange={e => setPassword(e.target.value)}
                      value={password}
                      className="rounded-[5px] border border-[#e1e1e1] bg-white p-[10.5px_14px] text-black"
                    />
                    <span
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer"
                    >
                      {showPassword ? (
                        <EyeIcon className="fill-gray-500" />
                      ) : (
                        <EyeCloseIcon className="fill-gray-500" />
                      )}
                    </span>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <p className="pt-2 text-sm text-red-500">{error}</p>
                  )}

                  {/* Forgot Password */}
                  <p className="font-poppins mt-2 cursor-pointer text-right text-sm text-[#8c8c8c]">
                    Forgot Password
                  </p>
                </div>

                {/* Submit Button */}
                <div>
                  <Button className="w-full" size="md" disabled={loading}>
                    {loading ? 'Logging in...' : 'Login'}
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
