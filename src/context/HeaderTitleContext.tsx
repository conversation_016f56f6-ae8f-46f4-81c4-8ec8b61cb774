'use client';
import { createContext, useContext, useState } from 'react';

const HeaderTitleContext = createContext<{
  title: string;
  setTitle: (title: string) => void;
}>({ title: '', setTitle: () => {} });

export const HeaderTitleProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [title, setTitle] = useState('Default Title');
  return (
    <HeaderTitleContext.Provider value={{ title, setTitle }}>
      {children}
    </HeaderTitleContext.Provider>
  );
};

export const useHeaderTitle = () => useContext(HeaderTitleContext);
