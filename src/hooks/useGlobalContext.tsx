'use client';
import { Nullable } from '@/type';
import { QueryClient } from '@tanstack/query-core';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import {
  Dispatch,
  PropsWithChildren,
  SetStateAction,
  createContext,
  useContext,
  useState,
} from 'react';

type GlobalContextProviderType = {
  count: number;
  setCount: Dispatch<SetStateAction<number>>;
};

const GlobalContext = createContext<Nullable<GlobalContextProviderType>>(null);
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false, staleTime: 2000 },
    mutations: { retry: false },
  },
});

export const GlobalContextProvider = (props: PropsWithChildren) => {
  const { children } = props;
  const [count, setCount] = useState(0);

  return (
    <GlobalContext.Provider
      value={{
        count,
        setCount,
      }}
    >
      <QueryClientProvider client={queryClient}>
        {children}
        <ReactQueryDevtools initialIsOpen={false} buttonPosition="top-left" />
      </QueryClientProvider>
    </GlobalContext.Provider>
  );
};

export function useGlobalContext() {
  return useContext(GlobalContext) as GlobalContextProviderType;
}
