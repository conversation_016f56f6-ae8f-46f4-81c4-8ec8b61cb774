'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { TbDashboard, TbCalendarDue } from 'react-icons/tb';
import { BsChatLeftDots } from 'react-icons/bs';
import { FiInfo } from 'react-icons/fi';
import { LuPhoneCall } from 'react-icons/lu';

const DriverWebBottomNav = () => {
  const [activeTab, setActiveTab] = useState('Schedule');
  const router = useRouter();

  const tabs = [
    {
      name: 'Dashboard',
      icon: <TbDashboard size={30} />,
      path: '/driver/dashboard',
    },
    {
      name: 'Schedule',
      icon: <TbCalendarDue size={30} />,
      path: '/driver/schedule',
    },
    { name: 'Chat', icon: <BsChatLeftDots size={30} />, path: '/driver/chat' },
    { name: 'Report', icon: <FiInfo size={30} />, path: '/driver/report' },
  ];

  const handleTabClick = tab => {
    setActiveTab(tab.name);
    router.push(tab.path); // 👉 Navigate to path
  };

  return (
    <div className="fixed bottom-0 z-50 block flex w-full items-center justify-around rounded-t-[30px] bg-white py-3 shadow-[0_4px_20px_rgba(0,0,0,0.1)] sm:hidden">
      {tabs.map(tab => (
        <button
          key={tab.name}
          onClick={() => handleTabClick(tab)}
          className="flex flex-col items-center text-sm"
        >
          <div
            className={`text-2xl ${
              activeTab === tab.name ? 'text-purple' : 'text-[#76787A]'
            }`}
          >
            {tab.icon}
          </div>
          <span
            className={`mt-1 ${
              activeTab === tab.name
                ? 'text-purple font-medium'
                : 'text-[#76787A]'
            }`}
          >
            {tab.name}
          </span>
        </button>
      ))}
      <div className="absolute top-[-5rem] right-4">
        <button className="rounded-full bg-[#FF5F5F] p-3 text-white shadow-lg">
          <LuPhoneCall />
        </button>
      </div>
    </div>
  );
};

export default DriverWebBottomNav;
