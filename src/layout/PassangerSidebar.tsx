'use client';

import React, { FC, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { HiOutlineHome } from 'react-icons/hi2';
import { useSidebar } from '@/context/SidebarContext';
import { PiHeadset } from 'react-icons/pi';
import { FiPhoneCall } from 'react-icons/fi';
import { LiaCarSideSolid } from 'react-icons/lia';
import { BsChatLeftDots } from 'react-icons/bs';

// Navigation items (no roles)
const navItems = [
  {
    icon: <HiOutlineHome size={20} />,
    name: 'Home',
    path: '/passanger/dashboard',
  },
  {
    icon: <LiaCarSideSolid size={20} />,
    name: 'My Rides',
    path: '/passanger/my-rides',
  },
  { icon: <BsChatLeftDots size={20} />, name: 'Chat', path: '/passanger/chat' },
  { icon: <PiHeadset size={20} />, name: 'Help', path: '/passanger/help' },
];

const PassangerSidebar: FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const pathname = usePathname();

  const isActive = useCallback((path: string) => path === pathname, [pathname]);

  return (
    <aside
      className={`fixed top-0 left-0 z-40 mt-16 h-screen border-r border-gray-200 px-5 text-gray-900 transition-all duration-300 lg:mt-0 dark:border-gray-800 dark:bg-gray-900 ${isExpanded || isMobileOpen || isHovered ? 'w-[236px]' : 'w-[90px]'} ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}
      style={{
        backgroundImage: "url('/images/Side-Bar.svg')",
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'bottom',
      }}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Logo */}
      <div
        className={`flex py-8 ${!isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'}`}
      >
        <Link href="/">
          {isExpanded || isHovered || isMobileOpen ? (
            <Image
              src="/images/sidebar/logo.png"
              alt="Logo"
              width={150}
              height={40}
              className="dark:hidden"
            />
          ) : (
            <Image
              src="/images/sidebar/logo-mini.png"
              alt="Logo"
              width={32}
              height={32}
            />
          )}
        </Link>
      </div>

      {/* Navigation */}
      <nav className="no-scrollbar flex flex-col overflow-y-auto">
        <ul className="mb-6 flex flex-col gap-6">
          {navItems.map(nav => {
            const active = isActive(nav.path);
            return (
              <li key={nav.name}>
                <Link
                  href={nav.path}
                  className={`flex items-center gap-4 rounded-md px-2 py-2 transition-all duration-200 ${active ? 'font-semibold text-white' : 'text-white/60 hover:text-white'} ${!isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start'}`}
                >
                  <span className="text-xl">{nav.icon}</span>
                  {(isExpanded || isHovered || isMobileOpen) && (
                    <span className="font-medium">{nav.name}</span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>

        {/* Emergency Button */}
        <div className="py-6">
          <button className="flex w-full items-center justify-center gap-3 rounded-full bg-[#FF5F5F] px-5 py-2 text-[14px] font-normal text-white shadow">
            <span className="rounded-full bg-white p-[8px] text-[#050013]">
              <FiPhoneCall size={20} />
            </span>
            Emergency support 24/7
          </button>
        </div>
      </nav>
    </aside>
  );
};

export default PassangerSidebar;
