import React from 'react';

type BadgeVariant = 'light' | 'solid';
type BadgeSize = 'sm' | 'md';
type BadgeColor =
  | 'primary'
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'light'
  | 'dark';

interface BadgeProps {
  variant?: BadgeVariant; // Light or solid variant
  size?: BadgeSize; // Badge size
  color?: BadgeColor; // Badge color
  startIcon?: React.ReactNode; // Icon at the start
  endIcon?: React.ReactNode; // Icon at the end
  children: React.ReactNode; // Badge content
}

const Badge: React.FC<BadgeProps> = ({
  variant = 'light',
  color = 'primary',
  size = 'md',
  startIcon,
  endIcon,
  children,
}) => {
  const baseStyles =
    'inline-flex items-center py-0.5 justify-center gap-1 rounded-full font-medium';

  // Define size styles
  const sizeStyles = {
    sm: 'text-theme-xs', // Smaller padding and font size
    md: 'text-sm', // Default padding and font size
  };

  // Define color styles for variants
  const variants = {
    light: {
      primary: 'text-brand-500 dark:bg-brand-500/15 dark:text-brand-400',
      success: 'text-success-600 dark:bg-success-500/15 dark:text-success-500',
      error: 'text-error-600 dark:bg-error-500/15 dark:text-error-500',
      warning: 'text-warning-600 dark:bg-warning-500/15 dark:text-orange-400',
      info: 'text-blue-light-500 dark:bg-blue-light-500/15 dark:text-blue-light-500',
      light: 'text-gray-700 dark:bg-white/5 dark:text-white/80',
      dark: 'text-white dark:bg-white/5 dark:text-white',
    },
    solid: {
      primary: 'bg-brand-500 text-white dark:text-white',
      success: 'text-white dark:text-white',
      error: 'text-white dark:text-white',
      warning: 'text-white dark:text-white',
      info: 'text-white dark:text-white',
      light: 'dark:bg-white/5 text-white dark:text-white/80',
      dark: 'text-white dark:text-white',
    },
  };

  // Get styles based on size and color variant
  const sizeClass = sizeStyles[size];
  const colorStyles = variants[variant][color];

  return (
    <span className={`${baseStyles} ${sizeClass} ${colorStyles}`}>
      {startIcon && <span className="mr-1">{startIcon}</span>}
      {children}
      {endIcon && <span className="ml-1">{endIcon}</span>}
    </span>
  );
};

export default Badge;
