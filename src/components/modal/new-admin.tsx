'use client';
import React, { useRef, useEffect } from 'react';

export default function AddAdminModal({
  isOpen,
  onClose,
  accessSections = [],
}) {
  const modalRef = useRef(null);

  // Close modal on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div className="custom-scrollbar fixed inset-0 z-999 flex">
      <div className="absolute inset-0 bg-black/30"></div>
      <div
        ref={modalRef}
        className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out"
      >
        {/* Header */}
        <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
          <h2 className="text-[20px] font-normal text-[#050013]">
            New Super Admins
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-red-500"
          >
            ✕
          </button>
        </div>

        {/* Form */}
        <form className="flex flex-col gap-6">
          {/* Basic Information */}
          <div className="px-6 py-3">
            <h3 className="mb-2 text-[14px] font-medium text-[#050013]">
              Basic Information
            </h3>
            <input
              type="text"
              placeholder="Full Name"
              className="mb-3 w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              type="email"
              placeholder="Email ID"
              className="w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Access Control Sections */}
          <div className="px-6 py-3">
            <h3 className="mb-4 text-[14px] font-medium text-[#050013]">
              Access Control and Permissions
            </h3>
            {accessSections.map((section, idx) => (
              <AccordionSection
                key={idx}
                title={section.title}
                items={section.items}
              />
            ))}
          </div>

          {/* Footer */}
          <div className="sticky bottom-0 border-t bg-white px-6 py-3">
            <button
              type="submit"
              className="float-right rounded-full bg-[#3707EF] px-6 py-2 text-white hover:bg-[#3d0cc0]"
            >
              Add
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Dummy AccordionSection placeholder
function AccordionSection({ title, items }) {
  return (
    <div className="mb-2 rounded border p-2">
      <h4 className="text-sm font-medium">{title}</h4>
      <ul className="list-disc pl-4 text-xs text-gray-600">
        {items.map((item, idx) => (
          <li key={idx}>{item}</li>
        ))}
      </ul>
    </div>
  );
}
