import { NextRequest, NextResponse } from 'next/server';

const publicRoutes = ['/signin', '/login'];
export function middleware(request: NextRequest) {
  console.log('here middleware');
  const { pathname } = request.nextUrl;
  const token = request.cookies.get('accessToken')?.value;
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }
  const isPublic = publicRoutes.includes(pathname);
  if (!isPublic && !token) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  if (isPublic && token) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  return NextResponse.next();
}
export const config = {
  matcher: ['/', '/signin', '/register', '/dashboard/:path*'],
};
